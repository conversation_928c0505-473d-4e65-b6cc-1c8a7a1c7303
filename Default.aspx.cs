using System;
using System.Configuration;
using System.Net.Mail;
using System.Web.UI;
using System.Text;
using System.IO;
using System.Collections.Generic;
using ASIWeb.Services;
using ASIWeb.Models;
using ASIWeb.DAL;

namespace ASIWeb
{
    public partial class _Default : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                // Initialize localization
                LocalizationService.AutoDetectLanguage();

                // Load dynamic content
                LoadProductCategories();
                LoadTestimonials();
                LoadClients();

                // Set initial language for client-side
                string currentLang = LocalizationService.GetCurrentLanguage();
                ClientScript.RegisterStartupScript(this.GetType(), "InitLanguage",
                    string.Format("window.currentLanguage = '{0}';", currentLang), true);
            }
        }

        /// <summary>
        /// Load product categories from database
        /// </summary>
        private void LoadProductCategories()
        {
            try
            {
                string currentLang = LocalizationService.GetCurrentLanguage();
                List<ProductCategory> categories = ContentDAL.GetProductCategories(currentLang);

                // Store in ViewState for client-side access if needed
                ViewState["ProductCategories"] = categories;
            }
            catch (Exception ex)
            {
                // Log error but don't break the page
                System.Diagnostics.Debug.WriteLine("Error loading product categories: " + ex.Message);
            }
        }

        /// <summary>
        /// Load testimonials from database
        /// </summary>
        private void LoadTestimonials()
        {
            try
            {
                string currentLang = LocalizationService.GetCurrentLanguage();
                List<Testimonial> testimonials = ContentDAL.GetTestimonials(currentLang);

                // Store in ViewState for client-side access if needed
                ViewState["Testimonials"] = testimonials;
            }
            catch (Exception ex)
            {
                // Log error but don't break the page
                System.Diagnostics.Debug.WriteLine("Error loading testimonials: " + ex.Message);
            }
        }

        /// <summary>
        /// Load clients from database
        /// </summary>
        private void LoadClients()
        {
            try
            {
                List<Client> clients = ContentDAL.GetClients();

                // Store in ViewState for client-side access if needed
                ViewState["Clients"] = clients;
            }
            catch (Exception ex)
            {
                // Log error but don't break the page
                System.Diagnostics.Debug.WriteLine("Error loading clients: " + ex.Message);
            }
        }

        protected void btnSubmit_Click(object sender, EventArgs e)
        {
            if (Page.IsValid)
            {
                try
                {
                    // Get form data
                    string name = txtName.Text.Trim();
                    string company = txtCompany.Text.Trim();
                    string email = txtEmail.Text.Trim();
                    string country = ddlCountry.SelectedValue;
                    string message = txtMessage.Text.Trim();

                    // Additional server-side validation
                    if (string.IsNullOrEmpty(name) || string.IsNullOrEmpty(company) ||
                        string.IsNullOrEmpty(email) || string.IsNullOrEmpty(country) ||
                        string.IsNullOrEmpty(message))
                    {
                        throw new ArgumentException("All fields are required.");
                    }

                    // Validate email format
                    if (!IsValidEmail(email))
                    {
                        throw new ArgumentException("Invalid email format.");
                    }

                    // Send email notification
                    SendQuoteRequest(name, company, email, country, message);

                    // Log the request (for tracking purposes)
                    LogQuoteRequest(name, company, email, country, message);

                    // Show success message
                    lblMessage.Text = "Thank you for your quote request! We will contact you within 24 hours.";
                    lblMessage.CssClass = "success-message";
                    lblMessage.Visible = true;

                    // Clear form
                    ClearForm();
                }
                catch (ArgumentException ex)
                {
                    // Show validation error message
                    lblMessage.Text = ex.Message;
                    lblMessage.CssClass = "error-message";
                    lblMessage.Visible = true;
                }
                catch (Exception ex)
                {
                    // Show generic error message
                    lblMessage.Text = "There was an error processing your request. Please try again or contact us <NAME_EMAIL>";
                    lblMessage.CssClass = "error-message";
                    lblMessage.Visible = true;

                    // Log error (in production, use proper logging framework)
                    LogError("Error in quote request", ex);
                }
            }
        }

        private void SendQuoteRequest(string name, string company, string email, string country, string message)
        {
            try
            {
                // Email configuration
                MailMessage mail = new MailMessage();
                mail.From = new MailAddress("<EMAIL>", "Allied Services International");
                mail.To.Add(ConfigurationManager.AppSettings["ContactEmail"] ?? "<EMAIL>");
                mail.Subject = string.Format("New Quote Request from {0} - {1}", company, DateTime.Now.ToString("yyyy-MM-dd HH:mm"));

                // Create HTML email body
                StringBuilder emailBody = new StringBuilder();
                emailBody.AppendLine("<!DOCTYPE html>");
                emailBody.AppendLine("<html><head><style>");
                emailBody.AppendLine("body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }");
                emailBody.AppendLine(".header { background: #1e3a8a; color: white; padding: 20px; text-align: center; }");
                emailBody.AppendLine(".content { padding: 20px; }");
                emailBody.AppendLine(".field { margin-bottom: 15px; }");
                emailBody.AppendLine(".label { font-weight: bold; color: #1e3a8a; }");
                emailBody.AppendLine(".footer { background: #f3f4f6; padding: 15px; text-align: center; font-size: 12px; color: #666; }");
                emailBody.AppendLine("</style></head><body>");

                emailBody.AppendLine("<div class='header'>");
                emailBody.AppendLine("<h2>New Quote Request - Allied Services International</h2>");
                emailBody.AppendLine("</div>");

                emailBody.AppendLine("<div class='content'>");
                emailBody.AppendLine("<div class='field'><span class='label'>Date:</span> " + DateTime.Now.ToString("MMMM dd, yyyy HH:mm") + "</div>");
                emailBody.AppendLine("<div class='field'><span class='label'>Name:</span> " + System.Web.HttpUtility.HtmlEncode(name) + "</div>");
                emailBody.AppendLine("<div class='field'><span class='label'>Company:</span> " + System.Web.HttpUtility.HtmlEncode(company) + "</div>");
                emailBody.AppendLine("<div class='field'><span class='label'>Email:</span> " + System.Web.HttpUtility.HtmlEncode(email) + "</div>");
                emailBody.AppendLine("<div class='field'><span class='label'>Country:</span> " + System.Web.HttpUtility.HtmlEncode(country) + "</div>");
                emailBody.AppendLine("<div class='field'><span class='label'>Message:</span><br>" + System.Web.HttpUtility.HtmlEncode(message).Replace("\n", "<br>") + "</div>");
                emailBody.AppendLine("</div>");

                emailBody.AppendLine("<div class='footer'>");
                emailBody.AppendLine("<p>This email was automatically generated from the Allied Services International website contact form.</p>");
                emailBody.AppendLine("<p>Please respond to the customer at: " + System.Web.HttpUtility.HtmlEncode(email) + "</p>");
                emailBody.AppendLine("</div>");

                emailBody.AppendLine("</body></html>");

                mail.Body = emailBody.ToString();
                mail.IsBodyHtml = true;

                // Add reply-to header
                mail.ReplyToList.Add(new MailAddress(email, name));

                // SMTP configuration - configure in web.config system.net/mailSettings
                SmtpClient smtp = new SmtpClient();

                // For development/testing, save to file instead of sending
                if (System.Web.HttpContext.Current.IsDebuggingEnabled)
                {
                    // Save email to file for testing
                    string emailPath = Server.MapPath("~/App_Data/Emails/");
                    if (!Directory.Exists(emailPath))
                        Directory.CreateDirectory(emailPath);

                    string fileName = string.Format("Quote_{0}_{1:yyyyMMdd_HHmmss}.html",
                        company.Replace(" ", "_"), DateTime.Now);
                    File.WriteAllText(Path.Combine(emailPath, fileName), mail.Body);

                    System.Diagnostics.Debug.WriteLine("Quote request saved to: " + fileName);
                }
                else
                {
                    // In production, actually send the email
                    smtp.Send(mail);
                }
            }
            catch (Exception ex)
            {
                throw new Exception("Failed to send quote request email: " + ex.Message);
            }
        }

        private void ClearForm()
        {
            txtName.Text = "";
            txtCompany.Text = "";
            txtEmail.Text = "";
            ddlCountry.SelectedIndex = 0;
            txtMessage.Text = "";
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        private void LogQuoteRequest(string name, string company, string email, string country, string message)
        {
            try
            {
                string logPath = Server.MapPath("~/App_Data/Logs/");
                if (!Directory.Exists(logPath))
                    Directory.CreateDirectory(logPath);

                string logFile = Path.Combine(logPath, "QuoteRequests_" + DateTime.Now.ToString("yyyyMM") + ".log");
                string logEntry = string.Format("{0:yyyy-MM-dd HH:mm:ss} | {1} | {2} | {3} | {4} | {5}{6}",
                    DateTime.Now, name, company, email, country, message.Replace("\r\n", " ").Replace("\n", " "), Environment.NewLine);

                File.AppendAllText(logFile, logEntry);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("Failed to log quote request: " + ex.Message);
            }
        }

        private void LogError(string context, Exception ex)
        {
            try
            {
                string logPath = Server.MapPath("~/App_Data/Logs/");
                if (!Directory.Exists(logPath))
                    Directory.CreateDirectory(logPath);

                string logFile = Path.Combine(logPath, "Errors_" + DateTime.Now.ToString("yyyyMM") + ".log");
                string logEntry = string.Format("{0:yyyy-MM-dd HH:mm:ss} | {1} | {2} | {3}{4}",
                    DateTime.Now, context, ex.Message, ex.StackTrace, Environment.NewLine);

                File.AppendAllText(logFile, logEntry);
            }
            catch
            {
                // If logging fails, at least write to debug output
                System.Diagnostics.Debug.WriteLine("Error logging failed: " + context + " - " + ex.Message);
            }
        }
    }
}
