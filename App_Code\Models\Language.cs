using System;
using System.Collections.Generic;

namespace ASIWeb.Models
{
    /// <summary>
    /// Language model for multi-language support
    /// </summary>
    public class Language
    {
        public int LanguageId { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string NativeName { get; set; }
        public bool IsActive { get; set; }
        public bool IsDefault { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
    }

    /// <summary>
    /// Translation model for storing translated content
    /// </summary>
    public class Translation
    {
        public int TranslationId { get; set; }
        public int LanguageId { get; set; }
        public string TranslationKey { get; set; }
        public string TranslationValue { get; set; }
        public string Category { get; set; }
        public string Description { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
        
        // Navigation properties
        public Language Language { get; set; }
    }

    /// <summary>
    /// Contact form submission model
    /// </summary>
    public class ContactSubmission
    {
        public int SubmissionId { get; set; }
        public string Name { get; set; }
        public string Company { get; set; }
        public string Email { get; set; }
        public string Country { get; set; }
        public string Message { get; set; }
        public string LanguageCode { get; set; }
        public string IPAddress { get; set; }
        public string UserAgent { get; set; }
        public DateTime SubmissionDate { get; set; }
        public bool IsProcessed { get; set; }
        public DateTime? ProcessedDate { get; set; }
        public string ProcessedBy { get; set; }
        public string Notes { get; set; }
    }

    /// <summary>
    /// Site configuration model
    /// </summary>
    public class SiteConfiguration
    {
        public int ConfigId { get; set; }
        public string ConfigKey { get; set; }
        public string ConfigValue { get; set; }
        public string Description { get; set; }
        public string Category { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
    }

    /// <summary>
    /// Client company model
    /// </summary>
    public class Client
    {
        public int ClientId { get; set; }
        public string Name { get; set; }
        public string LogoUrl { get; set; }
        public string Website { get; set; }
        public string Description { get; set; }
        public int DisplayOrder { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
    }

    /// <summary>
    /// Product category model
    /// </summary>
    public class ProductCategory
    {
        public int CategoryId { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string IconClass { get; set; }
        public string ImageUrl { get; set; }
        public int DisplayOrder { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
        
        // Navigation properties
        public List<ProductCategoryTranslation> Translations { get; set; }
    }

    /// <summary>
    /// Product category translations
    /// </summary>
    public class ProductCategoryTranslation
    {
        public int TranslationId { get; set; }
        public int CategoryId { get; set; }
        public int LanguageId { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
        
        // Navigation properties
        public ProductCategory Category { get; set; }
        public Language Language { get; set; }
    }

    /// <summary>
    /// Testimonial model
    /// </summary>
    public class Testimonial
    {
        public int TestimonialId { get; set; }
        public string ClientName { get; set; }
        public string ClientPosition { get; set; }
        public string ClientCompany { get; set; }
        public string Content { get; set; }
        public string ImageUrl { get; set; }
        public int Rating { get; set; }
        public int DisplayOrder { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
        
        // Navigation properties
        public List<TestimonialTranslation> Translations { get; set; }
    }

    /// <summary>
    /// Testimonial translations
    /// </summary>
    public class TestimonialTranslation
    {
        public int TranslationId { get; set; }
        public int TestimonialId { get; set; }
        public int LanguageId { get; set; }
        public string Content { get; set; }
        public string ClientPosition { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
        
        // Navigation properties
        public Testimonial Testimonial { get; set; }
        public Language Language { get; set; }
    }

    /// <summary>
    /// Response model for AJAX calls
    /// </summary>
    public class AjaxResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public object Data { get; set; }
        public List<string> Errors { get; set; }

        public AjaxResponse()
        {
            Errors = new List<string>();
        }
    }
}
