-- =============================================
-- Allied Services International Inc.
-- Database Schema for Multi-language Website
-- SQL Server 2008 R2 / 2012 Compatible
-- =============================================

USE [ASIWebDB]
GO

-- =============================================
-- Languages Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Languages]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[Languages](
        [LanguageId] [int] IDENTITY(1,1) NOT NULL,
        [Code] [nvarchar](5) NOT NULL,
        [Name] [nvarchar](50) NOT NULL,
        [NativeName] [nvarchar](50) NOT NULL,
        [IsActive] [bit] NOT NULL DEFAULT(1),
        [IsDefault] [bit] NOT NULL DEFAULT(0),
        [CreatedDate] [datetime] NOT NULL DEFAULT(GETDATE()),
        [ModifiedDate] [datetime] NULL,
        CONSTRAINT [PK_Languages] PRIMARY KEY CLUSTERED ([LanguageId] ASC),
        CONSTRAINT [UK_Languages_Code] UNIQUE NONCLUSTERED ([Code] ASC)
    )
END
GO

-- =============================================
-- Translations Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Translations]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[Translations](
        [TranslationId] [int] IDENTITY(1,1) NOT NULL,
        [LanguageId] [int] NOT NULL,
        [TranslationKey] [nvarchar](100) NOT NULL,
        [TranslationValue] [nvarchar](max) NOT NULL,
        [Category] [nvarchar](50) NULL,
        [Description] [nvarchar](255) NULL,
        [CreatedDate] [datetime] NOT NULL DEFAULT(GETDATE()),
        [ModifiedDate] [datetime] NULL,
        CONSTRAINT [PK_Translations] PRIMARY KEY CLUSTERED ([TranslationId] ASC),
        CONSTRAINT [FK_Translations_Languages] FOREIGN KEY([LanguageId]) REFERENCES [dbo].[Languages] ([LanguageId]),
        CONSTRAINT [UK_Translations_Key_Language] UNIQUE NONCLUSTERED ([LanguageId] ASC, [TranslationKey] ASC)
    )
END
GO

-- =============================================
-- Contact Submissions Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[ContactSubmissions]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[ContactSubmissions](
        [SubmissionId] [int] IDENTITY(1,1) NOT NULL,
        [Name] [nvarchar](100) NOT NULL,
        [Company] [nvarchar](100) NOT NULL,
        [Email] [nvarchar](100) NOT NULL,
        [Country] [nvarchar](50) NOT NULL,
        [Message] [nvarchar](max) NOT NULL,
        [LanguageCode] [nvarchar](5) NOT NULL,
        [IPAddress] [nvarchar](45) NULL,
        [UserAgent] [nvarchar](500) NULL,
        [SubmissionDate] [datetime] NOT NULL DEFAULT(GETDATE()),
        [IsProcessed] [bit] NOT NULL DEFAULT(0),
        [ProcessedDate] [datetime] NULL,
        [ProcessedBy] [nvarchar](50) NULL,
        [Notes] [nvarchar](max) NULL,
        CONSTRAINT [PK_ContactSubmissions] PRIMARY KEY CLUSTERED ([SubmissionId] ASC)
    )
END
GO

-- =============================================
-- Site Configuration Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[SiteConfiguration]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[SiteConfiguration](
        [ConfigId] [int] IDENTITY(1,1) NOT NULL,
        [ConfigKey] [nvarchar](50) NOT NULL,
        [ConfigValue] [nvarchar](max) NOT NULL,
        [Description] [nvarchar](255) NULL,
        [Category] [nvarchar](50) NULL,
        [IsActive] [bit] NOT NULL DEFAULT(1),
        [CreatedDate] [datetime] NOT NULL DEFAULT(GETDATE()),
        [ModifiedDate] [datetime] NULL,
        CONSTRAINT [PK_SiteConfiguration] PRIMARY KEY CLUSTERED ([ConfigId] ASC),
        CONSTRAINT [UK_SiteConfiguration_Key] UNIQUE NONCLUSTERED ([ConfigKey] ASC)
    )
END
GO

-- =============================================
-- Clients Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Clients]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[Clients](
        [ClientId] [int] IDENTITY(1,1) NOT NULL,
        [Name] [nvarchar](100) NOT NULL,
        [LogoUrl] [nvarchar](255) NULL,
        [Website] [nvarchar](255) NULL,
        [Description] [nvarchar](500) NULL,
        [DisplayOrder] [int] NOT NULL DEFAULT(0),
        [IsActive] [bit] NOT NULL DEFAULT(1),
        [CreatedDate] [datetime] NOT NULL DEFAULT(GETDATE()),
        [ModifiedDate] [datetime] NULL,
        CONSTRAINT [PK_Clients] PRIMARY KEY CLUSTERED ([ClientId] ASC)
    )
END
GO

-- =============================================
-- Product Categories Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[ProductCategories]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[ProductCategories](
        [CategoryId] [int] IDENTITY(1,1) NOT NULL,
        [Name] [nvarchar](100) NOT NULL,
        [Description] [nvarchar](500) NULL,
        [IconClass] [nvarchar](50) NULL,
        [ImageUrl] [nvarchar](255) NULL,
        [DisplayOrder] [int] NOT NULL DEFAULT(0),
        [IsActive] [bit] NOT NULL DEFAULT(1),
        [CreatedDate] [datetime] NOT NULL DEFAULT(GETDATE()),
        [ModifiedDate] [datetime] NULL,
        CONSTRAINT [PK_ProductCategories] PRIMARY KEY CLUSTERED ([CategoryId] ASC)
    )
END
GO

-- =============================================
-- Product Category Translations Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[ProductCategoryTranslations]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[ProductCategoryTranslations](
        [TranslationId] [int] IDENTITY(1,1) NOT NULL,
        [CategoryId] [int] NOT NULL,
        [LanguageId] [int] NOT NULL,
        [Name] [nvarchar](100) NOT NULL,
        [Description] [nvarchar](500) NULL,
        [CreatedDate] [datetime] NOT NULL DEFAULT(GETDATE()),
        [ModifiedDate] [datetime] NULL,
        CONSTRAINT [PK_ProductCategoryTranslations] PRIMARY KEY CLUSTERED ([TranslationId] ASC),
        CONSTRAINT [FK_ProductCategoryTranslations_Categories] FOREIGN KEY([CategoryId]) REFERENCES [dbo].[ProductCategories] ([CategoryId]),
        CONSTRAINT [FK_ProductCategoryTranslations_Languages] FOREIGN KEY([LanguageId]) REFERENCES [dbo].[Languages] ([LanguageId]),
        CONSTRAINT [UK_ProductCategoryTranslations] UNIQUE NONCLUSTERED ([CategoryId] ASC, [LanguageId] ASC)
    )
END
GO

-- =============================================
-- Testimonials Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Testimonials]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[Testimonials](
        [TestimonialId] [int] IDENTITY(1,1) NOT NULL,
        [ClientName] [nvarchar](100) NOT NULL,
        [ClientPosition] [nvarchar](100) NOT NULL,
        [ClientCompany] [nvarchar](100) NOT NULL,
        [Content] [nvarchar](max) NOT NULL,
        [ImageUrl] [nvarchar](255) NULL,
        [Rating] [int] NOT NULL DEFAULT(5),
        [DisplayOrder] [int] NOT NULL DEFAULT(0),
        [IsActive] [bit] NOT NULL DEFAULT(1),
        [CreatedDate] [datetime] NOT NULL DEFAULT(GETDATE()),
        [ModifiedDate] [datetime] NULL,
        CONSTRAINT [PK_Testimonials] PRIMARY KEY CLUSTERED ([TestimonialId] ASC),
        CONSTRAINT [CK_Testimonials_Rating] CHECK ([Rating] >= 1 AND [Rating] <= 5)
    )
END
GO

-- =============================================
-- Testimonial Translations Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[TestimonialTranslations]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[TestimonialTranslations](
        [TranslationId] [int] IDENTITY(1,1) NOT NULL,
        [TestimonialId] [int] NOT NULL,
        [LanguageId] [int] NOT NULL,
        [Content] [nvarchar](max) NOT NULL,
        [ClientPosition] [nvarchar](100) NOT NULL,
        [CreatedDate] [datetime] NOT NULL DEFAULT(GETDATE()),
        [ModifiedDate] [datetime] NULL,
        CONSTRAINT [PK_TestimonialTranslations] PRIMARY KEY CLUSTERED ([TranslationId] ASC),
        CONSTRAINT [FK_TestimonialTranslations_Testimonials] FOREIGN KEY([TestimonialId]) REFERENCES [dbo].[Testimonials] ([TestimonialId]),
        CONSTRAINT [FK_TestimonialTranslations_Languages] FOREIGN KEY([LanguageId]) REFERENCES [dbo].[Languages] ([LanguageId]),
        CONSTRAINT [UK_TestimonialTranslations] UNIQUE NONCLUSTERED ([TestimonialId] ASC, [LanguageId] ASC)
    )
END
GO

-- =============================================
-- Create Indexes for Performance
-- =============================================

-- Translations indexes
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[Translations]') AND name = N'IX_Translations_Key')
    CREATE NONCLUSTERED INDEX [IX_Translations_Key] ON [dbo].[Translations] ([TranslationKey] ASC)
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[Translations]') AND name = N'IX_Translations_Category')
    CREATE NONCLUSTERED INDEX [IX_Translations_Category] ON [dbo].[Translations] ([Category] ASC)
GO

-- Contact Submissions indexes
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[ContactSubmissions]') AND name = N'IX_ContactSubmissions_Date')
    CREATE NONCLUSTERED INDEX [IX_ContactSubmissions_Date] ON [dbo].[ContactSubmissions] ([SubmissionDate] DESC)
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[ContactSubmissions]') AND name = N'IX_ContactSubmissions_Processed')
    CREATE NONCLUSTERED INDEX [IX_ContactSubmissions_Processed] ON [dbo].[ContactSubmissions] ([IsProcessed] ASC)
GO

-- Product Categories indexes
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[ProductCategories]') AND name = N'IX_ProductCategories_Order')
    CREATE NONCLUSTERED INDEX [IX_ProductCategories_Order] ON [dbo].[ProductCategories] ([DisplayOrder] ASC, [IsActive] ASC)
GO

-- Testimonials indexes
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[Testimonials]') AND name = N'IX_Testimonials_Order')
    CREATE NONCLUSTERED INDEX [IX_Testimonials_Order] ON [dbo].[Testimonials] ([DisplayOrder] ASC, [IsActive] ASC)
GO

-- Clients indexes
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[Clients]') AND name = N'IX_Clients_Order')
    CREATE NONCLUSTERED INDEX [IX_Clients_Order] ON [dbo].[Clients] ([DisplayOrder] ASC, [IsActive] ASC)
GO

PRINT 'Database schema created successfully!'
GO
