using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using ASIWeb.Models;

namespace ASIWeb.DAL
{
    /// <summary>
    /// Data Access Layer for Content operations (Products, Testimonials, Clients)
    /// </summary>
    public static class ContentDAL
    {
        /// <summary>
        /// Get all active product categories with translations
        /// </summary>
        public static List<ProductCategory> GetProductCategories(string languageCode = "en")
        {
            List<ProductCategory> categories = new List<ProductCategory>();

            string sql = @"
                SELECT 
                    pc.CategoryId, pc.Name, pc.Description, pc.IconClass, pc.ImageUrl, 
                    pc.DisplayOrder, pc.IsActive, pc.CreatedDate, pc.ModifiedDate,
                    ISNULL(pct.Name, pc.Name) as TranslatedName,
                    ISNULL(pct.Description, pc.Description) as TranslatedDescription
                FROM ProductCategories pc
                LEFT JOIN ProductCategoryTranslations pct ON pc.CategoryId = pct.CategoryId
                LEFT JOIN Languages l ON pct.LanguageId = l.LanguageId AND l.Code = @LanguageCode
                WHERE pc.IsActive = 1
                ORDER BY pc.DisplayOrder, pc.Name";

            SqlParameter[] parameters = {
                DatabaseHelper.CreateParameter("@LanguageCode", languageCode, SqlDbType.NVarChar, 5)
            };

            DatabaseHelper.ExecuteReader(sql, reader =>
            {
                while (reader.Read())
                {
                    ProductCategory category = new ProductCategory
                    {
                        CategoryId = DatabaseHelper.GetSafeInt(reader, "CategoryId"),
                        Name = DatabaseHelper.GetSafeString(reader, "TranslatedName"),
                        Description = DatabaseHelper.GetSafeString(reader, "TranslatedDescription"),
                        IconClass = DatabaseHelper.GetSafeString(reader, "IconClass"),
                        ImageUrl = DatabaseHelper.GetSafeString(reader, "ImageUrl"),
                        DisplayOrder = DatabaseHelper.GetSafeInt(reader, "DisplayOrder"),
                        IsActive = DatabaseHelper.GetSafeBool(reader, "IsActive"),
                        CreatedDate = DatabaseHelper.GetSafeDateTime(reader, "CreatedDate") ?? DateTime.Now,
                        ModifiedDate = DatabaseHelper.GetSafeDateTime(reader, "ModifiedDate")
                    };
                    categories.Add(category);
                }
            }, CommandType.Text, parameters);

            return categories;
        }

        /// <summary>
        /// Get all active testimonials with translations
        /// </summary>
        public static List<Testimonial> GetTestimonials(string languageCode = "en")
        {
            List<Testimonial> testimonials = new List<Testimonial>();

            string sql = @"
                SELECT 
                    t.TestimonialId, t.ClientName, t.ClientPosition, t.ClientCompany, 
                    t.Content, t.ImageUrl, t.Rating, t.DisplayOrder, t.IsActive, 
                    t.CreatedDate, t.ModifiedDate,
                    ISNULL(tt.Content, t.Content) as TranslatedContent,
                    ISNULL(tt.ClientPosition, t.ClientPosition) as TranslatedPosition
                FROM Testimonials t
                LEFT JOIN TestimonialTranslations tt ON t.TestimonialId = tt.TestimonialId
                LEFT JOIN Languages l ON tt.LanguageId = l.LanguageId AND l.Code = @LanguageCode
                WHERE t.IsActive = 1
                ORDER BY t.DisplayOrder, t.CreatedDate DESC";

            SqlParameter[] parameters = {
                DatabaseHelper.CreateParameter("@LanguageCode", languageCode, SqlDbType.NVarChar, 5)
            };

            DatabaseHelper.ExecuteReader(sql, reader =>
            {
                while (reader.Read())
                {
                    Testimonial testimonial = new Testimonial
                    {
                        TestimonialId = DatabaseHelper.GetSafeInt(reader, "TestimonialId"),
                        ClientName = DatabaseHelper.GetSafeString(reader, "ClientName"),
                        ClientPosition = DatabaseHelper.GetSafeString(reader, "TranslatedPosition"),
                        ClientCompany = DatabaseHelper.GetSafeString(reader, "ClientCompany"),
                        Content = DatabaseHelper.GetSafeString(reader, "TranslatedContent"),
                        ImageUrl = DatabaseHelper.GetSafeString(reader, "ImageUrl"),
                        Rating = DatabaseHelper.GetSafeInt(reader, "Rating"),
                        DisplayOrder = DatabaseHelper.GetSafeInt(reader, "DisplayOrder"),
                        IsActive = DatabaseHelper.GetSafeBool(reader, "IsActive"),
                        CreatedDate = DatabaseHelper.GetSafeDateTime(reader, "CreatedDate") ?? DateTime.Now,
                        ModifiedDate = DatabaseHelper.GetSafeDateTime(reader, "ModifiedDate")
                    };
                    testimonials.Add(testimonial);
                }
            }, CommandType.Text, parameters);

            return testimonials;
        }

        /// <summary>
        /// Get all active clients
        /// </summary>
        public static List<Client> GetClients()
        {
            string sql = @"
                SELECT ClientId, Name, LogoUrl, Website, Description, DisplayOrder, 
                       IsActive, CreatedDate, ModifiedDate
                FROM Clients 
                WHERE IsActive = 1
                ORDER BY DisplayOrder, Name";

            DataTable dt = DatabaseHelper.ExecuteQuery(sql);
            return DatabaseHelper.ConvertDataTableToList<Client>(dt);
        }

        /// <summary>
        /// Get site configuration values
        /// </summary>
        public static Dictionary<string, string> GetSiteConfiguration()
        {
            Dictionary<string, string> config = new Dictionary<string, string>();

            string sql = @"
                SELECT ConfigKey, ConfigValue 
                FROM SiteConfiguration 
                WHERE IsActive = 1";

            DatabaseHelper.ExecuteReader(sql, reader =>
            {
                while (reader.Read())
                {
                    string key = DatabaseHelper.GetSafeString(reader, "ConfigKey");
                    string value = DatabaseHelper.GetSafeString(reader, "ConfigValue");
                    config[key] = value;
                }
            });

            return config;
        }

        /// <summary>
        /// Get site configuration value by key
        /// </summary>
        public static string GetConfigurationValue(string configKey, string defaultValue = "")
        {
            string sql = @"
                SELECT ConfigValue 
                FROM SiteConfiguration 
                WHERE ConfigKey = @ConfigKey AND IsActive = 1";

            SqlParameter[] parameters = {
                DatabaseHelper.CreateParameter("@ConfigKey", configKey, SqlDbType.NVarChar, 50)
            };

            object result = DatabaseHelper.ExecuteScalar(sql, CommandType.Text, parameters);
            return result != null ? result.ToString() : defaultValue;
        }

        /// <summary>
        /// Update site configuration value
        /// </summary>
        public static bool UpdateConfigurationValue(string configKey, string configValue)
        {
            try
            {
                string sql = @"
                    UPDATE SiteConfiguration 
                    SET ConfigValue = @ConfigValue, ModifiedDate = GETDATE()
                    WHERE ConfigKey = @ConfigKey";

                SqlParameter[] parameters = {
                    DatabaseHelper.CreateParameter("@ConfigKey", configKey, SqlDbType.NVarChar, 50),
                    DatabaseHelper.CreateParameter("@ConfigValue", configValue, SqlDbType.NVarChar)
                };

                int rowsAffected = DatabaseHelper.ExecuteNonQuery(sql, CommandType.Text, parameters);
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("Error in UpdateConfigurationValue: " + ex.Message);
                return false;
            }
        }

        /// <summary>
        /// Insert new product category
        /// </summary>
        public static int InsertProductCategory(ProductCategory category)
        {
            try
            {
                string sql = @"
                    INSERT INTO ProductCategories 
                    (Name, Description, IconClass, ImageUrl, DisplayOrder, IsActive)
                    VALUES 
                    (@Name, @Description, @IconClass, @ImageUrl, @DisplayOrder, @IsActive);
                    SELECT SCOPE_IDENTITY();";

                SqlParameter[] parameters = {
                    DatabaseHelper.CreateParameter("@Name", category.Name, SqlDbType.NVarChar, 100),
                    DatabaseHelper.CreateParameter("@Description", category.Description, SqlDbType.NVarChar, 500),
                    DatabaseHelper.CreateParameter("@IconClass", category.IconClass, SqlDbType.NVarChar, 50),
                    DatabaseHelper.CreateParameter("@ImageUrl", category.ImageUrl, SqlDbType.NVarChar, 255),
                    DatabaseHelper.CreateParameter("@DisplayOrder", category.DisplayOrder, SqlDbType.Int),
                    DatabaseHelper.CreateParameter("@IsActive", category.IsActive, SqlDbType.Bit)
                };

                object result = DatabaseHelper.ExecuteScalar(sql, CommandType.Text, parameters);
                return result != null ? Convert.ToInt32(result) : 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("Error in InsertProductCategory: " + ex.Message);
                throw;
            }
        }

        /// <summary>
        /// Insert new testimonial
        /// </summary>
        public static int InsertTestimonial(Testimonial testimonial)
        {
            try
            {
                string sql = @"
                    INSERT INTO Testimonials 
                    (ClientName, ClientPosition, ClientCompany, Content, ImageUrl, Rating, DisplayOrder, IsActive)
                    VALUES 
                    (@ClientName, @ClientPosition, @ClientCompany, @Content, @ImageUrl, @Rating, @DisplayOrder, @IsActive);
                    SELECT SCOPE_IDENTITY();";

                SqlParameter[] parameters = {
                    DatabaseHelper.CreateParameter("@ClientName", testimonial.ClientName, SqlDbType.NVarChar, 100),
                    DatabaseHelper.CreateParameter("@ClientPosition", testimonial.ClientPosition, SqlDbType.NVarChar, 100),
                    DatabaseHelper.CreateParameter("@ClientCompany", testimonial.ClientCompany, SqlDbType.NVarChar, 100),
                    DatabaseHelper.CreateParameter("@Content", testimonial.Content, SqlDbType.NVarChar),
                    DatabaseHelper.CreateParameter("@ImageUrl", testimonial.ImageUrl, SqlDbType.NVarChar, 255),
                    DatabaseHelper.CreateParameter("@Rating", testimonial.Rating, SqlDbType.Int),
                    DatabaseHelper.CreateParameter("@DisplayOrder", testimonial.DisplayOrder, SqlDbType.Int),
                    DatabaseHelper.CreateParameter("@IsActive", testimonial.IsActive, SqlDbType.Bit)
                };

                object result = DatabaseHelper.ExecuteScalar(sql, CommandType.Text, parameters);
                return result != null ? Convert.ToInt32(result) : 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("Error in InsertTestimonial: " + ex.Message);
                throw;
            }
        }

        /// <summary>
        /// Insert new client
        /// </summary>
        public static int InsertClient(Client client)
        {
            try
            {
                string sql = @"
                    INSERT INTO Clients 
                    (Name, LogoUrl, Website, Description, DisplayOrder, IsActive)
                    VALUES 
                    (@Name, @LogoUrl, @Website, @Description, @DisplayOrder, @IsActive);
                    SELECT SCOPE_IDENTITY();";

                SqlParameter[] parameters = {
                    DatabaseHelper.CreateParameter("@Name", client.Name, SqlDbType.NVarChar, 100),
                    DatabaseHelper.CreateParameter("@LogoUrl", client.LogoUrl, SqlDbType.NVarChar, 255),
                    DatabaseHelper.CreateParameter("@Website", client.Website, SqlDbType.NVarChar, 255),
                    DatabaseHelper.CreateParameter("@Description", client.Description, SqlDbType.NVarChar, 500),
                    DatabaseHelper.CreateParameter("@DisplayOrder", client.DisplayOrder, SqlDbType.Int),
                    DatabaseHelper.CreateParameter("@IsActive", client.IsActive, SqlDbType.Bit)
                };

                object result = DatabaseHelper.ExecuteScalar(sql, CommandType.Text, parameters);
                return result != null ? Convert.ToInt32(result) : 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("Error in InsertClient: " + ex.Message);
                throw;
            }
        }

        /// <summary>
        /// Update product category
        /// </summary>
        public static bool UpdateProductCategory(ProductCategory category)
        {
            try
            {
                string sql = @"
                    UPDATE ProductCategories 
                    SET Name = @Name, Description = @Description, IconClass = @IconClass, 
                        ImageUrl = @ImageUrl, DisplayOrder = @DisplayOrder, IsActive = @IsActive,
                        ModifiedDate = GETDATE()
                    WHERE CategoryId = @CategoryId";

                SqlParameter[] parameters = {
                    DatabaseHelper.CreateParameter("@CategoryId", category.CategoryId, SqlDbType.Int),
                    DatabaseHelper.CreateParameter("@Name", category.Name, SqlDbType.NVarChar, 100),
                    DatabaseHelper.CreateParameter("@Description", category.Description, SqlDbType.NVarChar, 500),
                    DatabaseHelper.CreateParameter("@IconClass", category.IconClass, SqlDbType.NVarChar, 50),
                    DatabaseHelper.CreateParameter("@ImageUrl", category.ImageUrl, SqlDbType.NVarChar, 255),
                    DatabaseHelper.CreateParameter("@DisplayOrder", category.DisplayOrder, SqlDbType.Int),
                    DatabaseHelper.CreateParameter("@IsActive", category.IsActive, SqlDbType.Bit)
                };

                int rowsAffected = DatabaseHelper.ExecuteNonQuery(sql, CommandType.Text, parameters);
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("Error in UpdateProductCategory: " + ex.Message);
                return false;
            }
        }
    }
}
