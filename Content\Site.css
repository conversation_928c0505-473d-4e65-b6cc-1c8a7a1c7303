/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-blue: #1e3a8a;
    --secondary-blue: #3b82f6;
    --metallic-gray: #6b7280;
    --light-gray: #f3f4f6;
    --dark-gray: #374151;
    --accent-red: #dc2626;
    --white: #ffffff;
    --text-dark: #1f2937;
    --text-light: #6b7280;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    background-color: var(--white);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Language Toggle */
.language-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
}

.language-toggle button {
    background: var(--primary-blue);
    color: var(--white);
    border: none;
    padding: 10px 15px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(30, 58, 138, 0.3);
}

.language-toggle button:hover {
    background: var(--secondary-blue);
    transform: translateY(-2px);
}

/* Navigation */
.navbar {
    background: var(--white);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;
    padding: 0;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 80px;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 12px;
}

.nav-logo img {
    height: 50px;
    width: auto;
}

.company-name {
    font-size: 18px;
    font-weight: 600;
    color: var(--primary-blue);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 40px;
    margin: 0;
}

.nav-menu a {
    text-decoration: none;
    color: var(--text-dark);
    font-weight: 500;
    font-size: 16px;
    transition: color 0.3s ease;
    position: relative;
}

.nav-menu a:hover {
    color: var(--primary-blue);
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--accent-red);
    transition: width 0.3s ease;
}

.nav-menu a:hover::after {
    width: 100%;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: var(--primary-blue);
    transition: all 0.3s ease;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
    color: var(--white);
    padding: 120px 0 80px;
    margin-top: 80px;
    overflow: hidden;
    position: relative;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.1;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
    position: relative;
    z-index: 1;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 24px;
    background: linear-gradient(45deg, var(--white), #e5e7eb);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.25rem;
    line-height: 1.6;
    margin-bottom: 32px;
    color: rgba(255, 255, 255, 0.9);
}

.cta-button {
    display: inline-block;
    background: var(--accent-red);
    color: var(--white);
    padding: 16px 32px;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    font-size: 18px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 20px rgba(220, 38, 38, 0.3);
}

.cta-button:hover {
    background: #b91c1c;
    transform: translateY(-2px);
    box-shadow: 0 6px 25px rgba(220, 38, 38, 0.4);
}

.hero-image img {
    width: 100%;
    height: auto;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

/* Section Styles */
section {
    padding: 80px 0;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 60px;
    color: var(--primary-blue);
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: var(--accent-red);
    border-radius: 2px;
}

/* Products Section */
.products {
    background: var(--light-gray);
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
}

.product-card {
    background: var(--white);
    padding: 40px 30px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 1px solid rgba(30, 58, 138, 0.1);
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.product-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 24px;
}

.product-icon i {
    font-size: 32px;
    color: var(--white);
}

.product-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 16px;
    color: var(--primary-blue);
}

.product-card p {
    color: var(--text-light);
    line-height: 1.6;
}

/* Benefits Section */
.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
}

.benefit-card {
    background: var(--white);
    padding: 40px 30px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border-left: 4px solid var(--accent-red);
}

.benefit-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.benefit-icon {
    width: 70px;
    height: 70px;
    background: var(--accent-red);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 24px;
}

.benefit-icon i {
    font-size: 28px;
    color: var(--white);
}

.benefit-card h3 {
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: 16px;
    color: var(--primary-blue);
}

.benefit-card p {
    color: var(--text-light);
    line-height: 1.6;
}

/* About Section */
.about {
    background: var(--light-gray);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.about-text h2 {
    text-align: left;
    margin-bottom: 30px;
}

.about-text p {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--text-light);
    margin-bottom: 20px;
}

.about-image img {
    width: 100%;
    height: auto;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* Clients Section */
.clients {
    background: var(--white);
    padding: 60px 0;
}

.clients-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 40px;
    align-items: center;
}

.client-logo {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    background: var(--light-gray);
    border-radius: 10px;
    transition: all 0.3s ease;
    min-height: 120px;
}

.client-logo:hover {
    background: var(--white);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.client-logo img {
    max-width: 150px;
    max-height: 80px;
    width: auto;
    height: auto;
    filter: grayscale(100%);
    transition: filter 0.3s ease;
}

.client-logo:hover img {
    filter: grayscale(0%);
}

/* Testimonials Section */
.testimonials {
    background: var(--primary-blue);
    color: var(--white);
}

.testimonials .section-title {
    color: var(--white);
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
}

.testimonial-card {
    background: rgba(255, 255, 255, 0.1);
    padding: 40px 30px;
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.testimonial-card:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-5px);
}

.testimonial-content p {
    font-size: 1.1rem;
    line-height: 1.7;
    margin-bottom: 24px;
    font-style: italic;
}

.testimonial-author strong {
    display: block;
    font-size: 1.1rem;
    margin-bottom: 4px;
}

.testimonial-author span {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.95rem;
}

/* Contact Section */
.contact {
    background: var(--light-gray);
}

.contact-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 60px;
}

.contact-form {
    background: var(--white);
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.form-group {
    margin-bottom: 24px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--primary-blue);
}

.form-control {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
    font-family: inherit;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

.submit-button {
    background: var(--primary-blue);
    color: var(--white);
    padding: 16px 32px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
}

.submit-button:hover {
    background: var(--secondary-blue);
    transform: translateY(-2px);
}

.error-message {
    color: var(--accent-red);
    font-size: 14px;
    margin-top: 4px;
    display: block;
}

.success-message {
    color: #059669;
    font-size: 16px;
    font-weight: 500;
    text-align: center;
    padding: 16px;
    background: #d1fae5;
    border-radius: 8px;
    margin-top: 16px;
}

.contact-info {
    background: var(--white);
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    height: fit-content;
}

.contact-info h3 {
    color: var(--primary-blue);
    margin-bottom: 30px;
    font-size: 1.5rem;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    margin-bottom: 24px;
}

.contact-item i {
    color: var(--accent-red);
    font-size: 20px;
    margin-top: 4px;
    min-width: 20px;
}

.contact-item strong {
    color: var(--primary-blue);
    display: block;
    margin-bottom: 4px;
}

.contact-item p {
    color: var(--text-light);
    margin: 0;
    line-height: 1.5;
}

/* Footer */
.footer {
    background: var(--dark-gray);
    color: var(--white);
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-section h4 {
    color: var(--white);
    margin-bottom: 20px;
    font-size: 1.2rem;
}

.footer-section p {
    color: #d1d5db;
    line-height: 1.6;
    margin-bottom: 16px;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 8px;
}

.footer-section ul li a {
    color: #d1d5db;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: var(--white);
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
}

.footer-logo img {
    height: 40px;
    width: auto;
}

.footer-logo .company-name {
    color: var(--white);
    font-size: 16px;
    font-weight: 600;
}

.footer-contact p {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
    color: #d1d5db;
}

.footer-contact i {
    color: var(--accent-red);
    width: 16px;
}

.social-links {
    display: flex;
    gap: 16px;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: var(--primary-blue);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: var(--accent-red);
    transform: translateY(-2px);
}

.footer-bottom {
    border-top: 1px solid #4b5563;
    padding-top: 20px;
    text-align: center;
}

.footer-bottom p {
    color: #9ca3af;
    margin: 0;
}

.footer-bottom a {
    color: #d1d5db;
    text-decoration: none;
}

.footer-bottom a:hover {
    color: var(--white);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .hero-container {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }

    .hero-title {
        font-size: 3rem;
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }
}

@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 80px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 80px);
        background: var(--white);
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: 60px;
        transition: left 0.3s ease;
        box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    }

    .nav-menu.active {
        left: 0;
    }

    .hamburger {
        display: flex;
    }

    .hamburger.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .hamburger.active span:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }

    .hero {
        padding: 100px 0 60px;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .products-grid {
        grid-template-columns: 1fr;
    }

    .benefits-grid {
        grid-template-columns: 1fr;
    }

    .testimonials-grid {
        grid-template-columns: 1fr;
    }

    .clients-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .language-toggle {
        top: 10px;
        right: 10px;
    }

    .language-toggle button {
        padding: 8px 12px;
        font-size: 12px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .clients-grid {
        grid-template-columns: 1fr;
    }

    .contact-form,
    .contact-info {
        padding: 30px 20px;
    }

    .company-name {
        display: none;
    }
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Loading Animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}
