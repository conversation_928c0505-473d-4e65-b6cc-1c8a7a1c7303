using System;
using System.Collections.Generic;
using System.Web;
using System.Web.Services;
using System.Web.UI;
using ASIWeb.Services;
using ASIWeb.Models;
using Newtonsoft.Json;

public partial class Pages_LanguageHandler : System.Web.UI.Page
{
    protected void Page_Load(object sender, EventArgs e)
    {
        // This page is used only for AJAX calls
    }

    /// <summary>
    /// Get all translations for a specific language
    /// </summary>
    [WebMethod]
    public static string GetTranslations(string languageCode)
    {
        try
        {
            if (string.IsNullOrEmpty(languageCode))
            {
                languageCode = "en";
            }

            // Validate language code
            if (!LocalizationService.IsLanguageSupported(languageCode))
            {
                languageCode = "en";
            }

            Dictionary<string, string> translations = LocalizationService.GetTranslations(languageCode);
            
            AjaxResponse response = new AjaxResponse
            {
                Success = true,
                Message = "Translations loaded successfully",
                Data = translations
            };

            return JsonConvert.SerializeObject(response);
        }
        catch (Exception ex)
        {
            AjaxResponse response = new AjaxResponse
            {
                Success = false,
                Message = "Error loading translations: " + ex.Message,
                Errors = new List<string> { ex.Message }
            };

            return JsonConvert.SerializeObject(response);
        }
    }

    /// <summary>
    /// Set current language
    /// </summary>
    [WebMethod]
    public static string SetLanguage(string languageCode)
    {
        try
        {
            if (string.IsNullOrEmpty(languageCode))
            {
                throw new ArgumentException("Language code is required");
            }

            // Validate language code
            if (!LocalizationService.IsLanguageSupported(languageCode))
            {
                throw new ArgumentException("Language not supported: " + languageCode);
            }

            LocalizationService.SetCurrentLanguage(languageCode);

            AjaxResponse response = new AjaxResponse
            {
                Success = true,
                Message = "Language set successfully",
                Data = new { LanguageCode = languageCode }
            };

            return JsonConvert.SerializeObject(response);
        }
        catch (Exception ex)
        {
            AjaxResponse response = new AjaxResponse
            {
                Success = false,
                Message = "Error setting language: " + ex.Message,
                Errors = new List<string> { ex.Message }
            };

            return JsonConvert.SerializeObject(response);
        }
    }

    /// <summary>
    /// Get available languages
    /// </summary>
    [WebMethod]
    public static string GetAvailableLanguages()
    {
        try
        {
            List<Language> languages = LocalizationService.GetAvailableLanguages();

            AjaxResponse response = new AjaxResponse
            {
                Success = true,
                Message = "Languages loaded successfully",
                Data = languages
            };

            return JsonConvert.SerializeObject(response);
        }
        catch (Exception ex)
        {
            AjaxResponse response = new AjaxResponse
            {
                Success = false,
                Message = "Error loading languages: " + ex.Message,
                Errors = new List<string> { ex.Message }
            };

            return JsonConvert.SerializeObject(response);
        }
    }

    /// <summary>
    /// Get translations by category
    /// </summary>
    [WebMethod]
    public static string GetTranslationsByCategory(string languageCode, string category)
    {
        try
        {
            if (string.IsNullOrEmpty(languageCode))
            {
                languageCode = "en";
            }

            if (string.IsNullOrEmpty(category))
            {
                throw new ArgumentException("Category is required");
            }

            // Validate language code
            if (!LocalizationService.IsLanguageSupported(languageCode))
            {
                languageCode = "en";
            }

            Dictionary<string, string> translations = LocalizationService.GetTranslationsByCategory(category, languageCode);

            AjaxResponse response = new AjaxResponse
            {
                Success = true,
                Message = "Category translations loaded successfully",
                Data = translations
            };

            return JsonConvert.SerializeObject(response);
        }
        catch (Exception ex)
        {
            AjaxResponse response = new AjaxResponse
            {
                Success = false,
                Message = "Error loading category translations: " + ex.Message,
                Errors = new List<string> { ex.Message }
            };

            return JsonConvert.SerializeObject(response);
        }
    }

    /// <summary>
    /// Get current language
    /// </summary>
    [WebMethod]
    public static string GetCurrentLanguage()
    {
        try
        {
            string currentLanguage = LocalizationService.GetCurrentLanguage();

            AjaxResponse response = new AjaxResponse
            {
                Success = true,
                Message = "Current language retrieved successfully",
                Data = new { LanguageCode = currentLanguage }
            };

            return JsonConvert.SerializeObject(response);
        }
        catch (Exception ex)
        {
            AjaxResponse response = new AjaxResponse
            {
                Success = false,
                Message = "Error getting current language: " + ex.Message,
                Errors = new List<string> { ex.Message }
            };

            return JsonConvert.SerializeObject(response);
        }
    }

    /// <summary>
    /// Clear translation cache
    /// </summary>
    [WebMethod]
    public static string ClearCache(string languageCode = null)
    {
        try
        {
            LocalizationService.ClearTranslationCache(languageCode);

            AjaxResponse response = new AjaxResponse
            {
                Success = true,
                Message = "Cache cleared successfully"
            };

            return JsonConvert.SerializeObject(response);
        }
        catch (Exception ex)
        {
            AjaxResponse response = new AjaxResponse
            {
                Success = false,
                Message = "Error clearing cache: " + ex.Message,
                Errors = new List<string> { ex.Message }
            };

            return JsonConvert.SerializeObject(response);
        }
    }
}
