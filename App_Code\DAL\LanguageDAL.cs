using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using ASIWeb.Models;

namespace ASIWeb.DAL
{
    /// <summary>
    /// Data Access Layer for Language and Translation operations
    /// </summary>
    public static class LanguageDAL
    {
        /// <summary>
        /// Get all active languages
        /// </summary>
        public static List<Language> GetActiveLanguages()
        {
            string sql = @"
                SELECT LanguageId, Code, Name, NativeName, IsActive, IsDefault, CreatedDate, ModifiedDate
                FROM Languages 
                WHERE IsActive = 1 
                ORDER BY IsDefault DESC, Name";

            DataTable dt = DatabaseHelper.ExecuteQuery(sql);
            return DatabaseHelper.ConvertDataTableToList<Language>(dt);
        }

        /// <summary>
        /// Get language by code
        /// </summary>
        public static Language GetLanguageByCode(string languageCode)
        {
            string sql = @"
                SELECT LanguageId, Code, Name, NativeName, IsActive, IsDefault, CreatedDate, ModifiedDate
                FROM Languages 
                WHERE Code = @Code AND IsActive = 1";

            SqlParameter[] parameters = {
                DatabaseHelper.CreateParameter("@Code", languageCode, SqlDbType.NVarChar, 5)
            };

            DataTable dt = DatabaseHelper.ExecuteQuery(sql, CommandType.Text, parameters);
            List<Language> languages = DatabaseHelper.ConvertDataTableToList<Language>(dt);
            
            return languages.Count > 0 ? languages[0] : null;
        }

        /// <summary>
        /// Get default language
        /// </summary>
        public static Language GetDefaultLanguage()
        {
            string sql = @"
                SELECT LanguageId, Code, Name, NativeName, IsActive, IsDefault, CreatedDate, ModifiedDate
                FROM Languages 
                WHERE IsDefault = 1 AND IsActive = 1";

            DataTable dt = DatabaseHelper.ExecuteQuery(sql);
            List<Language> languages = DatabaseHelper.ConvertDataTableToList<Language>(dt);
            
            return languages.Count > 0 ? languages[0] : null;
        }

        /// <summary>
        /// Get all translations for a specific language
        /// </summary>
        public static Dictionary<string, string> GetTranslations(string languageCode)
        {
            Dictionary<string, string> translations = new Dictionary<string, string>();

            string sql = @"
                SELECT t.TranslationKey, t.TranslationValue
                FROM Translations t
                INNER JOIN Languages l ON t.LanguageId = l.LanguageId
                WHERE l.Code = @LanguageCode AND l.IsActive = 1
                ORDER BY t.TranslationKey";

            SqlParameter[] parameters = {
                DatabaseHelper.CreateParameter("@LanguageCode", languageCode, SqlDbType.NVarChar, 5)
            };

            DatabaseHelper.ExecuteReader(sql, reader =>
            {
                while (reader.Read())
                {
                    string key = DatabaseHelper.GetSafeString(reader, "TranslationKey");
                    string value = DatabaseHelper.GetSafeString(reader, "TranslationValue");
                    
                    if (!translations.ContainsKey(key))
                    {
                        translations.Add(key, value);
                    }
                }
            }, CommandType.Text, parameters);

            return translations;
        }

        /// <summary>
        /// Get translations by category
        /// </summary>
        public static Dictionary<string, string> GetTranslationsByCategory(string languageCode, string category)
        {
            Dictionary<string, string> translations = new Dictionary<string, string>();

            string sql = @"
                SELECT t.TranslationKey, t.TranslationValue
                FROM Translations t
                INNER JOIN Languages l ON t.LanguageId = l.LanguageId
                WHERE l.Code = @LanguageCode AND l.IsActive = 1 AND t.Category = @Category
                ORDER BY t.TranslationKey";

            SqlParameter[] parameters = {
                DatabaseHelper.CreateParameter("@LanguageCode", languageCode, SqlDbType.NVarChar, 5),
                DatabaseHelper.CreateParameter("@Category", category, SqlDbType.NVarChar, 50)
            };

            DatabaseHelper.ExecuteReader(sql, reader =>
            {
                while (reader.Read())
                {
                    string key = DatabaseHelper.GetSafeString(reader, "TranslationKey");
                    string value = DatabaseHelper.GetSafeString(reader, "TranslationValue");
                    
                    if (!translations.ContainsKey(key))
                    {
                        translations.Add(key, value);
                    }
                }
            }, CommandType.Text, parameters);

            return translations;
        }

        /// <summary>
        /// Get single translation
        /// </summary>
        public static string GetTranslation(string languageCode, string translationKey, string defaultValue = "")
        {
            string sql = @"
                SELECT t.TranslationValue
                FROM Translations t
                INNER JOIN Languages l ON t.LanguageId = l.LanguageId
                WHERE l.Code = @LanguageCode AND l.IsActive = 1 AND t.TranslationKey = @TranslationKey";

            SqlParameter[] parameters = {
                DatabaseHelper.CreateParameter("@LanguageCode", languageCode, SqlDbType.NVarChar, 5),
                DatabaseHelper.CreateParameter("@TranslationKey", translationKey, SqlDbType.NVarChar, 100)
            };

            object result = DatabaseHelper.ExecuteScalar(sql, CommandType.Text, parameters);
            return result != null ? result.ToString() : defaultValue;
        }

        /// <summary>
        /// Add or update translation
        /// </summary>
        public static bool UpsertTranslation(string languageCode, string translationKey, string translationValue, string category = null, string description = null)
        {
            try
            {
                string sql = @"
                    DECLARE @LanguageId INT = (SELECT LanguageId FROM Languages WHERE Code = @LanguageCode AND IsActive = 1)
                    
                    IF @LanguageId IS NULL
                        RETURN
                    
                    IF EXISTS (SELECT 1 FROM Translations WHERE LanguageId = @LanguageId AND TranslationKey = @TranslationKey)
                    BEGIN
                        UPDATE Translations 
                        SET TranslationValue = @TranslationValue,
                            Category = ISNULL(@Category, Category),
                            Description = ISNULL(@Description, Description),
                            ModifiedDate = GETDATE()
                        WHERE LanguageId = @LanguageId AND TranslationKey = @TranslationKey
                    END
                    ELSE
                    BEGIN
                        INSERT INTO Translations (LanguageId, TranslationKey, TranslationValue, Category, Description)
                        VALUES (@LanguageId, @TranslationKey, @TranslationValue, @Category, @Description)
                    END";

                SqlParameter[] parameters = {
                    DatabaseHelper.CreateParameter("@LanguageCode", languageCode, SqlDbType.NVarChar, 5),
                    DatabaseHelper.CreateParameter("@TranslationKey", translationKey, SqlDbType.NVarChar, 100),
                    DatabaseHelper.CreateParameter("@TranslationValue", translationValue, SqlDbType.NVarChar),
                    DatabaseHelper.CreateParameter("@Category", category, SqlDbType.NVarChar, 50),
                    DatabaseHelper.CreateParameter("@Description", description, SqlDbType.NVarChar, 255)
                };

                int rowsAffected = DatabaseHelper.ExecuteNonQuery(sql, CommandType.Text, parameters);
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("Error in UpsertTranslation: " + ex.Message);
                return false;
            }
        }

        /// <summary>
        /// Delete translation
        /// </summary>
        public static bool DeleteTranslation(string languageCode, string translationKey)
        {
            try
            {
                string sql = @"
                    DELETE t FROM Translations t
                    INNER JOIN Languages l ON t.LanguageId = l.LanguageId
                    WHERE l.Code = @LanguageCode AND t.TranslationKey = @TranslationKey";

                SqlParameter[] parameters = {
                    DatabaseHelper.CreateParameter("@LanguageCode", languageCode, SqlDbType.NVarChar, 5),
                    DatabaseHelper.CreateParameter("@TranslationKey", translationKey, SqlDbType.NVarChar, 100)
                };

                int rowsAffected = DatabaseHelper.ExecuteNonQuery(sql, CommandType.Text, parameters);
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("Error in DeleteTranslation: " + ex.Message);
                return false;
            }
        }

        /// <summary>
        /// Get translation categories
        /// </summary>
        public static List<string> GetTranslationCategories()
        {
            List<string> categories = new List<string>();

            string sql = @"
                SELECT DISTINCT Category 
                FROM Translations 
                WHERE Category IS NOT NULL 
                ORDER BY Category";

            DatabaseHelper.ExecuteReader(sql, reader =>
            {
                while (reader.Read())
                {
                    string category = DatabaseHelper.GetSafeString(reader, "Category");
                    if (!string.IsNullOrEmpty(category))
                    {
                        categories.Add(category);
                    }
                }
            });

            return categories;
        }

        /// <summary>
        /// Get translation statistics
        /// </summary>
        public static Dictionary<string, int> GetTranslationStats()
        {
            Dictionary<string, int> stats = new Dictionary<string, int>();

            string sql = @"
                SELECT 
                    l.Code,
                    COUNT(t.TranslationId) as TranslationCount
                FROM Languages l
                LEFT JOIN Translations t ON l.LanguageId = t.LanguageId
                WHERE l.IsActive = 1
                GROUP BY l.Code, l.Name
                ORDER BY l.Name";

            DatabaseHelper.ExecuteReader(sql, reader =>
            {
                while (reader.Read())
                {
                    string code = DatabaseHelper.GetSafeString(reader, "Code");
                    int count = DatabaseHelper.GetSafeInt(reader, "TranslationCount");
                    stats[code] = count;
                }
            });

            return stats;
        }
    }
}
