
using System;
using System.Collections.Generic;
using System.Web;
using System.Web.Caching;
using ASIWeb.DAL;
using ASIWeb.Models;

namespace ASIWeb.Services
{
    /// <summary>
    /// Service for handling localization and translations
    /// </summary>
    public static class LocalizationService
    {
        private const string CACHE_KEY_TRANSLATIONS = "Translations_{0}";
        private const string CACHE_KEY_LANGUAGES = "Languages";
        private const int CACHE_DURATION_MINUTES = 30;

        /// <summary>
        /// Get current language from session or default
        /// </summary>
        public static string GetCurrentLanguage()
        {
            if (HttpContext.Current != null && HttpContext.Current.Session != null)
            {
                string sessionLang = HttpContext.Current.Session["CurrentLanguage"] as string;
                if (!string.IsNullOrEmpty(sessionLang))
                {
                    return sessionLang;
                }
            }

            // Try to get from cookie
            if (HttpContext.Current != null && HttpContext.Current.Request.Cookies["PreferredLanguage"] != null)
            {
                string cookieLang = HttpContext.Current.Request.Cookies["PreferredLanguage"].Value;
                if (!string.IsNullOrEmpty(cookieLang))
                {
                    SetCurrentLanguage(cookieLang);
                    return cookieLang;
                }
            }

            // Default to English
            return "en";
        }

        /// <summary>
        /// Set current language in session and cookie
        /// </summary>
        public static void SetCurrentLanguage(string languageCode)
        {
            if (HttpContext.Current != null)
            {
                // Set in session
                if (HttpContext.Current.Session != null)
                {
                    HttpContext.Current.Session["CurrentLanguage"] = languageCode;
                }

                // Set in cookie (expires in 30 days)
                HttpCookie cookie = new HttpCookie("PreferredLanguage", languageCode)
                {
                    Expires = DateTime.Now.AddDays(30),
                    HttpOnly = true
                };
                HttpContext.Current.Response.Cookies.Add(cookie);
            }
        }

        /// <summary>
        /// Get all translations for a language (cached)
        /// </summary>
        public static Dictionary<string, string> GetTranslations(string languageCode)
        {
            // Carga traducciones con caching automático
            Dictionary<string, string> translations = LanguageDAL.GetTranslations(languageCode);
            return translations;
        }

        /// <summary>
        /// Get single translation
        /// </summary>
        public static string GetTranslation(string key, string languageCode = null, string defaultValue = "")
        {
            if (string.IsNullOrEmpty(languageCode))
            {
                languageCode = GetCurrentLanguage();
            }

            Dictionary<string, string> translations = GetTranslations(languageCode);
            
            if (translations.ContainsKey(key))
            {
                return translations[key];
            }

            // If not found and not English, try English as fallback
            if (languageCode != "en")
            {
                Dictionary<string, string> englishTranslations = GetTranslations("en");
                if (englishTranslations.ContainsKey(key))
                {
                    return englishTranslations[key];
                }
            }

            return !string.IsNullOrEmpty(defaultValue) ? defaultValue : key;
        }

        /// <summary>
        /// Get translations by category
        /// </summary>
        public static Dictionary<string, string> GetTranslationsByCategory(string category, string languageCode = null)
        {
            if (string.IsNullOrEmpty(languageCode))
            {
                languageCode = GetCurrentLanguage();
            }

            return LanguageDAL.GetTranslationsByCategory(languageCode, category);
        }

        /// <summary>
        /// Get all available languages (cached)
        /// </summary>
        public static List<Language> GetAvailableLanguages()
        {
            if (HttpContext.Current != null && HttpContext.Current.Cache != null)
            {
                List<Language> cachedLanguages = HttpContext.Current.Cache[CACHE_KEY_LANGUAGES] as List<Language>;
                if (cachedLanguages != null)
                {
                    return cachedLanguages;
                }
            }

            // Load from database
            List<Language> languages = LanguageDAL.GetActiveLanguages();

            // Cache the languages
            if (HttpContext.Current != null && HttpContext.Current.Cache != null)
            {
                HttpContext.Current.Cache.Insert(
                    CACHE_KEY_LANGUAGES,
                    languages,
                    null,
                    DateTime.Now.AddMinutes(CACHE_DURATION_MINUTES),
                    TimeSpan.Zero,
                    CacheItemPriority.Normal,
                    null
                );
            }

            return languages;
        }

        /// <summary>
        /// Get language by code
        /// </summary>
        public static Language GetLanguage(string languageCode)
        {
            List<Language> languages = GetAvailableLanguages();
            return languages.Find(l => l.Code.Equals(languageCode, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// Check if language is supported
        /// </summary>
        public static bool IsLanguageSupported(string languageCode)
        {
            return GetLanguage(languageCode) != null;
        }

        /// <summary>
        /// Get default language
        /// </summary>
        public static Language GetDefaultLanguage()
        {
            List<Language> languages = GetAvailableLanguages();
            Language defaultLang = languages.Find(l => l.IsDefault);
            return defaultLang ?? languages.Find(l => l.Code == "en") ?? (languages.Count > 0 ? languages[0] : null);
        }

        /// <summary>
        /// Clear translation cache
        /// </summary>
        public static void ClearTranslationCache(string languageCode = null)
        {
            if (HttpContext.Current != null && HttpContext.Current.Cache != null)
            {
                if (!string.IsNullOrEmpty(languageCode))
                {
                    string cacheKey = string.Format(CACHE_KEY_TRANSLATIONS, languageCode);
                    HttpContext.Current.Cache.Remove(cacheKey);
                }
                else
                {
                    // Clear all translation caches
                    List<Language> languages = GetAvailableLanguages();
                    foreach (Language lang in languages)
                    {
                        string cacheKey = string.Format(CACHE_KEY_TRANSLATIONS, lang.Code);
                        HttpContext.Current.Cache.Remove(cacheKey);
                    }
                }

                // Clear languages cache
                HttpContext.Current.Cache.Remove(CACHE_KEY_LANGUAGES);
            }
        }

        /// <summary>
        /// Format message with parameters
        /// </summary>
        public static string FormatTranslation(string key, params object[] args)
        {
            string translation = GetTranslation(key);
            
            if (args != null && args.Length > 0)
            {
                try
                {
                    return string.Format(translation, args);
                }
                catch
                {
                    return translation;
                }
            }
            
            return translation;
        }

        /// <summary>
        /// Get localized date format
        /// </summary>
        public static string GetLocalizedDateFormat(string languageCode = null)
        {
            if (string.IsNullOrEmpty(languageCode))
            {
                languageCode = GetCurrentLanguage();
            }

            switch (languageCode.ToLower())
            {
                case "es":
                    return "dd/MM/yyyy";
                case "en":
                default:
                    return "MM/dd/yyyy";
            }
        }

        /// <summary>
        /// Get localized currency format
        /// </summary>
        public static string GetLocalizedCurrencyFormat(string languageCode = null)
        {
            if (string.IsNullOrEmpty(languageCode))
            {
                languageCode = GetCurrentLanguage();
            }

            switch (languageCode.ToLower())
            {
                case "es":
                    return "C"; // Uses system locale
                case "en":
                default:
                    return "C"; // Uses system locale
            }
        }

        /// <summary>
        /// Get browser preferred language
        /// </summary>
        public static string GetBrowserPreferredLanguage()
        {
            if (HttpContext.Current != null && HttpContext.Current.Request.UserLanguages != null)
            {
                foreach (string userLang in HttpContext.Current.Request.UserLanguages)
                {
                    string langCode = userLang.Split('-')[0].ToLower();
                    if (IsLanguageSupported(langCode))
                    {
                        return langCode;
                    }
                }
            }

            return "en"; // Default fallback
        }

        /// <summary>
        /// Auto-detect and set language based on browser preferences
        /// </summary>
        public static void AutoDetectLanguage()
        {
            string currentLang = GetCurrentLanguage();
            
            // Only auto-detect if no language is set
            if (currentLang == "en" && HttpContext.Current != null && HttpContext.Current.Session != null && HttpContext.Current.Session["CurrentLanguage"] == null)
            {
                string browserLang = GetBrowserPreferredLanguage();
                if (browserLang != "en")
                {
                    SetCurrentLanguage(browserLang);
                }
            }
        }

        /// <summary>
        /// Get RTL (Right-to-Left) direction for language
        /// </summary>
        public static bool IsRightToLeft(string languageCode = null)
        {
            if (string.IsNullOrEmpty(languageCode))
            {
                languageCode = GetCurrentLanguage();
            }

            // Add RTL languages as needed
            string[] rtlLanguages = { "ar", "he", "fa", "ur" };
            return Array.IndexOf(rtlLanguages, languageCode.ToLower()) >= 0;
        }

        /// <summary>
        /// Get text direction CSS class
        /// </summary>
        public static string GetTextDirectionClass(string languageCode = null)
        {
            return IsRightToLeft(languageCode) ? "rtl" : "ltr";
        }
    }
}

