using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using ASIWeb.Models;

namespace ASIWeb.DAL
{
    /// <summary>
    /// Data Access Layer for Contact operations
    /// </summary>
    public static class ContactDAL
    {
        /// <summary>
        /// Insert contact submission
        /// </summary>
        public static int InsertContactSubmission(ContactSubmission submission)
        {
            try
            {
                string sql = @"
                    INSERT INTO ContactSubmissions 
                    (Name, Company, Email, Country, Message, LanguageCode, IPAddress, UserAgent, SubmissionDate)
                    VALUES 
                    (@Name, @Company, @Email, @Country, @Message, @LanguageCode, @IPAddress, @UserAgent, @SubmissionDate);
                    SELECT SCOPE_IDENTITY();";

                SqlParameter[] parameters = {
                    DatabaseHelper.CreateParameter("@Name", submission.Name, SqlDbType.NVarChar, 100),
                    DatabaseHelper.CreateParameter("@Company", submission.Company, SqlDbType.NVarChar, 100),
                    DatabaseHelper.CreateParameter("@Email", submission.Email, SqlDbType.NVarChar, 100),
                    DatabaseHelper.CreateParameter("@Country", submission.Country, SqlDbType.NVarChar, 50),
                    DatabaseHelper.CreateParameter("@Message", submission.Message, SqlDbType.NVarChar),
                    DatabaseHelper.CreateParameter("@LanguageCode", submission.LanguageCode, SqlDbType.NVarChar, 5),
                    DatabaseHelper.CreateParameter("@IPAddress", submission.IPAddress, SqlDbType.NVarChar, 45),
                    DatabaseHelper.CreateParameter("@UserAgent", submission.UserAgent, SqlDbType.NVarChar, 500),
                    DatabaseHelper.CreateParameter("@SubmissionDate", submission.SubmissionDate, SqlDbType.DateTime)
                };

                object result = DatabaseHelper.ExecuteScalar(sql, CommandType.Text, parameters);
                return result != null ? Convert.ToInt32(result) : 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("Error in InsertContactSubmission: " + ex.Message);
                throw;
            }
        }

        /// <summary>
        /// Get contact submission by ID
        /// </summary>
        public static ContactSubmission GetContactSubmission(int submissionId)
        {
            string sql = @"
                SELECT SubmissionId, Name, Company, Email, Country, Message, LanguageCode, 
                       IPAddress, UserAgent, SubmissionDate, IsProcessed, ProcessedDate, ProcessedBy, Notes
                FROM ContactSubmissions 
                WHERE SubmissionId = @SubmissionId";

            SqlParameter[] parameters = {
                DatabaseHelper.CreateParameter("@SubmissionId", submissionId, SqlDbType.Int)
            };

            DataTable dt = DatabaseHelper.ExecuteQuery(sql, CommandType.Text, parameters);
            List<ContactSubmission> submissions = DatabaseHelper.ConvertDataTableToList<ContactSubmission>(dt);
            
            return submissions.Count > 0 ? submissions[0] : null;
        }

        /// <summary>
        /// Get all contact submissions with pagination
        /// </summary>
        public static List<ContactSubmission> GetContactSubmissions(int pageNumber = 1, int pageSize = 50, bool unprocessedOnly = false)
        {
            string sql = @"
                SELECT SubmissionId, Name, Company, Email, Country, Message, LanguageCode, 
                       IPAddress, UserAgent, SubmissionDate, IsProcessed, ProcessedDate, ProcessedBy, Notes
                FROM ContactSubmissions 
                WHERE (@UnprocessedOnly = 0 OR IsProcessed = 0)
                ORDER BY SubmissionDate DESC
                OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY";

            int offset = (pageNumber - 1) * pageSize;

            SqlParameter[] parameters = {
                DatabaseHelper.CreateParameter("@Offset", offset, SqlDbType.Int),
                DatabaseHelper.CreateParameter("@PageSize", pageSize, SqlDbType.Int),
                DatabaseHelper.CreateParameter("@UnprocessedOnly", unprocessedOnly, SqlDbType.Bit)
            };

            DataTable dt = DatabaseHelper.ExecuteQuery(sql, CommandType.Text, parameters);
            return DatabaseHelper.ConvertDataTableToList<ContactSubmission>(dt);
        }

        /// <summary>
        /// Get contact submissions count
        /// </summary>
        public static int GetContactSubmissionsCount(bool unprocessedOnly = false)
        {
            string sql = @"
                SELECT COUNT(*) 
                FROM ContactSubmissions 
                WHERE (@UnprocessedOnly = 0 OR IsProcessed = 0)";

            SqlParameter[] parameters = {
                DatabaseHelper.CreateParameter("@UnprocessedOnly", unprocessedOnly, SqlDbType.Bit)
            };

            object result = DatabaseHelper.ExecuteScalar(sql, CommandType.Text, parameters);
            return result != null ? Convert.ToInt32(result) : 0;
        }

        /// <summary>
        /// Mark contact submission as processed
        /// </summary>
        public static bool MarkAsProcessed(int submissionId, string processedBy, string notes = null)
        {
            try
            {
                string sql = @"
                    UPDATE ContactSubmissions 
                    SET IsProcessed = 1, 
                        ProcessedDate = GETDATE(), 
                        ProcessedBy = @ProcessedBy,
                        Notes = @Notes
                    WHERE SubmissionId = @SubmissionId";

                SqlParameter[] parameters = {
                    DatabaseHelper.CreateParameter("@SubmissionId", submissionId, SqlDbType.Int),
                    DatabaseHelper.CreateParameter("@ProcessedBy", processedBy, SqlDbType.NVarChar, 50),
                    DatabaseHelper.CreateParameter("@Notes", notes, SqlDbType.NVarChar)
                };

                int rowsAffected = DatabaseHelper.ExecuteNonQuery(sql, CommandType.Text, parameters);
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("Error in MarkAsProcessed: " + ex.Message);
                return false;
            }
        }

        /// <summary>
        /// Delete contact submission
        /// </summary>
        public static bool DeleteContactSubmission(int submissionId)
        {
            try
            {
                string sql = "DELETE FROM ContactSubmissions WHERE SubmissionId = @SubmissionId";

                SqlParameter[] parameters = {
                    DatabaseHelper.CreateParameter("@SubmissionId", submissionId, SqlDbType.Int)
                };

                int rowsAffected = DatabaseHelper.ExecuteNonQuery(sql, CommandType.Text, parameters);
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("Error in DeleteContactSubmission: " + ex.Message);
                return false;
            }
        }

        /// <summary>
        /// Get contact submissions by date range
        /// </summary>
        public static List<ContactSubmission> GetContactSubmissionsByDateRange(DateTime startDate, DateTime endDate)
        {
            string sql = @"
                SELECT SubmissionId, Name, Company, Email, Country, Message, LanguageCode, 
                       IPAddress, UserAgent, SubmissionDate, IsProcessed, ProcessedDate, ProcessedBy, Notes
                FROM ContactSubmissions 
                WHERE SubmissionDate >= @StartDate AND SubmissionDate <= @EndDate
                ORDER BY SubmissionDate DESC";

            SqlParameter[] parameters = {
                DatabaseHelper.CreateParameter("@StartDate", startDate, SqlDbType.DateTime),
                DatabaseHelper.CreateParameter("@EndDate", endDate, SqlDbType.DateTime)
            };

            DataTable dt = DatabaseHelper.ExecuteQuery(sql, CommandType.Text, parameters);
            return DatabaseHelper.ConvertDataTableToList<ContactSubmission>(dt);
        }

        /// <summary>
        /// Get contact submission statistics
        /// </summary>
        public static Dictionary<string, object> GetContactStatistics()
        {
            Dictionary<string, object> stats = new Dictionary<string, object>();

            string sql = @"
                SELECT 
                    COUNT(*) as TotalSubmissions,
                    COUNT(CASE WHEN IsProcessed = 1 THEN 1 END) as ProcessedSubmissions,
                    COUNT(CASE WHEN IsProcessed = 0 THEN 1 END) as UnprocessedSubmissions,
                    COUNT(CASE WHEN SubmissionDate >= DATEADD(day, -7, GETDATE()) THEN 1 END) as LastWeekSubmissions,
                    COUNT(CASE WHEN SubmissionDate >= DATEADD(day, -30, GETDATE()) THEN 1 END) as LastMonthSubmissions
                FROM ContactSubmissions";

            DatabaseHelper.ExecuteReader(sql, reader =>
            {
                if (reader.Read())
                {
                    stats["TotalSubmissions"] = DatabaseHelper.GetSafeInt(reader, "TotalSubmissions");
                    stats["ProcessedSubmissions"] = DatabaseHelper.GetSafeInt(reader, "ProcessedSubmissions");
                    stats["UnprocessedSubmissions"] = DatabaseHelper.GetSafeInt(reader, "UnprocessedSubmissions");
                    stats["LastWeekSubmissions"] = DatabaseHelper.GetSafeInt(reader, "LastWeekSubmissions");
                    stats["LastMonthSubmissions"] = DatabaseHelper.GetSafeInt(reader, "LastMonthSubmissions");
                }
            });

            return stats;
        }

        /// <summary>
        /// Get submissions by country
        /// </summary>
        public static Dictionary<string, int> GetSubmissionsByCountry()
        {
            Dictionary<string, int> countryStats = new Dictionary<string, int>();

            string sql = @"
                SELECT Country, COUNT(*) as SubmissionCount
                FROM ContactSubmissions 
                GROUP BY Country
                ORDER BY SubmissionCount DESC";

            DatabaseHelper.ExecuteReader(sql, reader =>
            {
                while (reader.Read())
                {
                    string country = DatabaseHelper.GetSafeString(reader, "Country");
                    int count = DatabaseHelper.GetSafeInt(reader, "SubmissionCount");
                    countryStats[country] = count;
                }
            });

            return countryStats;
        }

        /// <summary>
        /// Get submissions by language
        /// </summary>
        public static Dictionary<string, int> GetSubmissionsByLanguage()
        {
            Dictionary<string, int> languageStats = new Dictionary<string, int>();

            string sql = @"
                SELECT LanguageCode, COUNT(*) as SubmissionCount
                FROM ContactSubmissions 
                GROUP BY LanguageCode
                ORDER BY SubmissionCount DESC";

            DatabaseHelper.ExecuteReader(sql, reader =>
            {
                while (reader.Read())
                {
                    string language = DatabaseHelper.GetSafeString(reader, "LanguageCode");
                    int count = DatabaseHelper.GetSafeInt(reader, "SubmissionCount");
                    languageStats[language] = count;
                }
            });

            return languageStats;
        }

        /// <summary>
        /// Search contact submissions
        /// </summary>
        public static List<ContactSubmission> SearchContactSubmissions(string searchTerm, int pageNumber = 1, int pageSize = 50)
        {
            string sql = @"
                SELECT SubmissionId, Name, Company, Email, Country, Message, LanguageCode, 
                       IPAddress, UserAgent, SubmissionDate, IsProcessed, ProcessedDate, ProcessedBy, Notes
                FROM ContactSubmissions 
                WHERE Name LIKE @SearchTerm 
                   OR Company LIKE @SearchTerm 
                   OR Email LIKE @SearchTerm 
                   OR Country LIKE @SearchTerm
                   OR Message LIKE @SearchTerm
                ORDER BY SubmissionDate DESC
                OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY";

            int offset = (pageNumber - 1) * pageSize;
            string searchPattern = "%" + searchTerm + "%";

            SqlParameter[] parameters = {
                DatabaseHelper.CreateParameter("@SearchTerm", searchPattern, SqlDbType.NVarChar),
                DatabaseHelper.CreateParameter("@Offset", offset, SqlDbType.Int),
                DatabaseHelper.CreateParameter("@PageSize", pageSize, SqlDbType.Int)
            };

            DataTable dt = DatabaseHelper.ExecuteQuery(sql, CommandType.Text, parameters);
            return DatabaseHelper.ConvertDataTableToList<ContactSubmission>(dt);
        }
    }
}
