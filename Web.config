<?xml version="1.0"?>
<configuration>
  <connectionStrings>
    <!-- SQL Server Connection String for ASI Website -->
    <add name="DefaultConnection" connectionString="Data Source=(LocalDB)\MSSQLLocalDB;AttachDbFilename=|DataDirectory|\ASIWebDB.mdf;Integrated Security=True;Connect Timeout=30" providerName="System.Data.SqlClient"/>
    <!-- Alternative for SQL Server Express -->
    <!-- <add name="DefaultConnection" connectionString="Data Source=.\SQLEXPRESS;Initial Catalog=ASIWebDB;Integrated Security=True" providerName="System.Data.SqlClient" /> -->
    <!-- Alternative for full SQL Server -->
    <!-- <add name="DefaultConnection" connectionString="Data Source=server;Initial Catalog=ASIWebDB;User ID=username;Password=password" providerName="System.Data.SqlClient" /> -->
  </connectionStrings>
  <!--
    Para obtener una descripción de los cambios de web.config, vea http://go.microsoft.com/fwlink/?LinkId=235367.

    Los siguientes atributos se pueden establecer en la etiqueta <httpRuntime>.
      <system.Web>
        <httpRuntime targetFramework="4.8" />
      </system.Web>
  -->
  <system.web>
    <compilation debug="true" targetFramework="4.8">
      <assemblies>
        <add assembly="System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35"/>
        <add assembly="System.Data.Linq, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089"/>
      </assemblies>
    </compilation>
    <httpRuntime targetFramework="4.0" maxRequestLength="51200" executionTimeout="3600"/>
    <pages controlRenderingCompatibilityVersion="4.0" clientIDMode="AutoID"/>
    <globalization culture="en-US" uiCulture="en-US"/>
    <sessionState timeout="60"/>
    <httpHandlers>
      <add verb="*" path="*.aspx" type="System.Web.UI.PageHandlerFactory"/>
    </httpHandlers>
  </system.web>
  <system.webServer>
    <defaultDocument>
      <files>
        <clear/>
        <add value="Default.aspx"/>
        <add value="index.html"/>
      </files>
    </defaultDocument>
    <staticContent>
      <mimeMap fileExtension=".woff" mimeType="application/font-woff"/>
      <mimeMap fileExtension=".woff2" mimeType="application/font-woff2"/>
    </staticContent>
  </system.webServer>
  <appSettings>
    <add key="CompanyName" value="Allied Services International Inc."/>
    <add key="CompanyAddress" value="6405 NW 36th St Suite # 116, Miami, FL 33166, Estados Unidos"/>
    <add key="PeruAddress" value="Calle Ricardo Angulo 745 - San Isidro, Perú"/>
    <add key="ContactEmail" value="<EMAIL>"/>
    <add key="ContactPhone" value="+****************"/>
  </appSettings>
</configuration>