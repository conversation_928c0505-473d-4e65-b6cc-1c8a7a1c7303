// Global variables
let currentLanguage = 'en';

// DOM Content Loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeNavigation();
    initializeLanguageToggle();
    initializeScrollAnimations();
    initializeSmoothScrolling();
    initializeFormValidation();
});

// Navigation functionality
function initializeNavigation() {
    const hamburger = document.getElementById('hamburger');
    const navMenu = document.getElementById('nav-menu');
    
    if (hamburger && navMenu) {
        hamburger.addEventListener('click', function() {
            hamburger.classList.toggle('active');
            navMenu.classList.toggle('active');
        });
        
        // Close menu when clicking on a link
        const navLinks = navMenu.querySelectorAll('a');
        navLinks.forEach(link => {
            link.addEventListener('click', function() {
                hamburger.classList.remove('active');
                navMenu.classList.remove('active');
            });
        });
        
        // Close menu when clicking outside
        document.addEventListener('click', function(event) {
            if (!hamburger.contains(event.target) && !navMenu.contains(event.target)) {
                hamburger.classList.remove('active');
                navMenu.classList.remove('active');
            }
        });
    }
}

// Language toggle functionality
function initializeLanguageToggle() {
    const langToggle = document.getElementById('langToggle');
    const langText = document.getElementById('langText');
    
    if (langToggle && langText) {
        langToggle.addEventListener('click', toggleLanguage);
    }
    
    // Set initial language
    setLanguage(currentLanguage);
}

function toggleLanguage() {
    currentLanguage = currentLanguage === 'en' ? 'es' : 'en';
    setLanguage(currentLanguage);
    
    // Update button text
    const langText = document.getElementById('langText');
    if (langText) {
        langText.textContent = currentLanguage === 'en' ? 'Español' : 'English';
    }
}

function setLanguage(lang) {
    const elements = document.querySelectorAll('[data-en][data-es]');
    
    elements.forEach(element => {
        const text = element.getAttribute('data-' + lang);
        if (text) {
            if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
                element.placeholder = text;
            } else {
                element.textContent = text;
            }
        }
    });
    
    // Update form labels and placeholders
    updateFormLanguage(lang);
    
    // Update document language attribute
    document.documentElement.lang = lang;
}

function updateFormLanguage(lang) {
    const formElements = {
        'txtName': {
            en: 'Enter your full name',
            es: 'Ingrese su nombre completo'
        },
        'txtCompany': {
            en: 'Enter your company name',
            es: 'Ingrese el nombre de su empresa'
        },
        'txtEmail': {
            en: 'Enter your email address',
            es: 'Ingrese su correo electrónico'
        },
        'txtMessage': {
            en: 'Describe your spare parts requirements...',
            es: 'Describa sus requerimientos de repuestos...'
        }
    };
    
    Object.keys(formElements).forEach(id => {
        const element = document.getElementById(id);
        if (element && formElements[id][lang]) {
            element.placeholder = formElements[id][lang];
        }
    });
    
    // Update dropdown first option
    const countryDropdown = document.getElementById('ddlCountry');
    if (countryDropdown && countryDropdown.options[0]) {
        countryDropdown.options[0].text = lang === 'en' ? 'Select your country' : 'Seleccione su país';
    }
    
    // Update submit button
    const submitButton = document.getElementById('btnSubmit');
    if (submitButton) {
        submitButton.value = lang === 'en' ? 'Send Quote Request' : 'Enviar Solicitud de Cotización';
    }
}

// Scroll animations
function initializeScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in-up');
            }
        });
    }, observerOptions);
    
    // Observe elements for animation
    const animatedElements = document.querySelectorAll('.product-card, .benefit-card, .testimonial-card, .client-logo');
    animatedElements.forEach(element => {
        observer.observe(element);
    });
}

// Smooth scrolling for navigation links
function initializeSmoothScrolling() {
    const navLinks = document.querySelectorAll('a[href^="#"]');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);
            
            if (targetElement) {
                const offsetTop = targetElement.offsetTop - 80; // Account for fixed navbar
                
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });
}

// Form validation
function initializeFormValidation() {
    const form = document.querySelector('form');
    if (!form) return;
    
    const inputs = form.querySelectorAll('input[type="text"], input[type="email"], textarea, select');
    
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            validateField(this);
        });
        
        input.addEventListener('input', function() {
            clearFieldError(this);
        });
    });
}

function validateField(field) {
    const value = field.value.trim();
    let isValid = true;
    let errorMessage = '';
    
    // Required field validation
    if (field.hasAttribute('required') || field.id === 'txtName' || field.id === 'txtCompany' || field.id === 'txtEmail' || field.id === 'ddlCountry' || field.id === 'txtMessage') {
        if (!value) {
            isValid = false;
            errorMessage = currentLanguage === 'en' ? 'This field is required' : 'Este campo es obligatorio';
        }
    }
    
    // Email validation
    if (field.type === 'email' && value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
            isValid = false;
            errorMessage = currentLanguage === 'en' ? 'Please enter a valid email address' : 'Por favor ingrese un correo electrónico válido';
        }
    }
    
    // Show/hide error
    if (!isValid) {
        showFieldError(field, errorMessage);
    } else {
        clearFieldError(field);
    }
    
    return isValid;
}

function showFieldError(field, message) {
    clearFieldError(field);
    
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message';
    errorDiv.textContent = message;
    
    field.parentNode.appendChild(errorDiv);
    field.classList.add('error');
}

function clearFieldError(field) {
    const errorMessage = field.parentNode.querySelector('.error-message');
    if (errorMessage) {
        errorMessage.remove();
    }
    field.classList.remove('error');
}

// Utility functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Window scroll event for navbar
window.addEventListener('scroll', debounce(function() {
    const navbar = document.querySelector('.navbar');
    if (navbar) {
        if (window.scrollY > 100) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    }
}, 10));

// Expose functions globally for ASP.NET compatibility
window.toggleLanguage = toggleLanguage;
window.setLanguage = setLanguage;
