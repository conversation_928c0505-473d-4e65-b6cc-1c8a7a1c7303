using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Collections.Generic;

namespace ASIWeb.DAL
{
    /// <summary>
    /// Database helper class for common database operations
    /// </summary>
    public static class DatabaseHelper
    {
        private static readonly string ConnectionString = ConfigurationManager.ConnectionStrings["DefaultConnection"].ConnectionString;

        /// <summary>
        /// Execute a stored procedure or SQL command that returns data
        /// </summary>
        public static DataTable ExecuteQuery(string commandText, CommandType commandType = CommandType.Text, params SqlParameter[] parameters)
        {
            DataTable dataTable = new DataTable();
            
            try
            {
                using (SqlConnection connection = new SqlConnection(ConnectionString))
                {
                    using (SqlCommand command = new SqlCommand(commandText, connection))
                    {
                        command.CommandType = commandType;
                        command.CommandTimeout = 30;
                        
                        if (parameters != null)
                        {
                            command.Parameters.AddRange(parameters);
                        }
                        
                        connection.Open();
                        using (SqlDataAdapter adapter = new SqlDataAdapter(command))
                        {
                            adapter.Fill(dataTable);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogError("ExecuteQuery", ex, commandText);
                throw;
            }
            
            return dataTable;
        }

        /// <summary>
        /// Execute a stored procedure or SQL command that doesn't return data
        /// </summary>
        public static int ExecuteNonQuery(string commandText, CommandType commandType = CommandType.Text, params SqlParameter[] parameters)
        {
            int rowsAffected = 0;
            
            try
            {
                using (SqlConnection connection = new SqlConnection(ConnectionString))
                {
                    using (SqlCommand command = new SqlCommand(commandText, connection))
                    {
                        command.CommandType = commandType;
                        command.CommandTimeout = 30;
                        
                        if (parameters != null)
                        {
                            command.Parameters.AddRange(parameters);
                        }
                        
                        connection.Open();
                        rowsAffected = command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                LogError("ExecuteNonQuery", ex, commandText);
                throw;
            }
            
            return rowsAffected;
        }

        /// <summary>
        /// Execute a stored procedure or SQL command that returns a single value
        /// </summary>
        public static object ExecuteScalar(string commandText, CommandType commandType = CommandType.Text, params SqlParameter[] parameters)
        {
            object result = null;
            
            try
            {
                using (SqlConnection connection = new SqlConnection(ConnectionString))
                {
                    using (SqlCommand command = new SqlCommand(commandText, connection))
                    {
                        command.CommandType = commandType;
                        command.CommandTimeout = 30;
                        
                        if (parameters != null)
                        {
                            command.Parameters.AddRange(parameters);
                        }
                        
                        connection.Open();
                        result = command.ExecuteScalar();
                    }
                }
            }
            catch (Exception ex)
            {
                LogError("ExecuteScalar", ex, commandText);
                throw;
            }
            
            return result;
        }

        /// <summary>
        /// Execute a stored procedure or SQL command with a data reader
        /// </summary>
        public static void ExecuteReader(string commandText, Action<SqlDataReader> readerAction, CommandType commandType = CommandType.Text, params SqlParameter[] parameters)
        {
            try
            {
                using (SqlConnection connection = new SqlConnection(ConnectionString))
                {
                    using (SqlCommand command = new SqlCommand(commandText, connection))
                    {
                        command.CommandType = commandType;
                        command.CommandTimeout = 30;
                        
                        if (parameters != null)
                        {
                            command.Parameters.AddRange(parameters);
                        }
                        
                        connection.Open();
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            readerAction(reader);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogError("ExecuteReader", ex, commandText);
                throw;
            }
        }

        /// <summary>
        /// Create SQL parameter
        /// </summary>
        public static SqlParameter CreateParameter(string parameterName, object value, SqlDbType sqlDbType = SqlDbType.NVarChar, int size = -1)
        {
            SqlParameter parameter = new SqlParameter(parameterName, sqlDbType);
            
            if (size > 0)
            {
                parameter.Size = size;
            }
            
            parameter.Value = value ?? DBNull.Value;
            return parameter;
        }

        /// <summary>
        /// Create SQL parameter with direction
        /// </summary>
        public static SqlParameter CreateParameter(string parameterName, object value, SqlDbType sqlDbType, ParameterDirection direction, int size = -1)
        {
            SqlParameter parameter = CreateParameter(parameterName, value, sqlDbType, size);
            parameter.Direction = direction;
            return parameter;
        }

        /// <summary>
        /// Test database connection
        /// </summary>
        public static bool TestConnection()
        {
            try
            {
                using (SqlConnection connection = new SqlConnection(ConnectionString))
                {
                    connection.Open();
                    return connection.State == ConnectionState.Open;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Get database connection string
        /// </summary>
        public static string GetConnectionString()
        {
            return ConnectionString;
        }

        /// <summary>
        /// Log database errors
        /// </summary>
        private static void LogError(string method, Exception ex, string commandText = "")
        {
            try
            {
                string logMessage = string.Format(
                    "Database Error in {0}: {1}\nCommand: {2}\nStackTrace: {3}",
                    method, ex.Message, commandText, ex.StackTrace
                );
                
                // Log to Windows Event Log or file
                System.Diagnostics.EventLog.WriteEntry("ASIWeb", logMessage, System.Diagnostics.EventLogEntryType.Error);
            }
            catch
            {
                // If logging fails, don't throw another exception
            }
        }

        /// <summary>
        /// Convert DataTable to List of objects
        /// </summary>
        public static List<T> ConvertDataTableToList<T>(DataTable dataTable) where T : new()
        {
            List<T> list = new List<T>();
            
            if (dataTable == null || dataTable.Rows.Count == 0)
                return list;

            var properties = typeof(T).GetProperties();
            
            foreach (DataRow row in dataTable.Rows)
            {
                T item = new T();
                
                foreach (var property in properties)
                {
                    if (dataTable.Columns.Contains(property.Name) && row[property.Name] != DBNull.Value)
                    {
                        property.SetValue(item, Convert.ChangeType(row[property.Name], property.PropertyType), null);
                    }
                }
                
                list.Add(item);
            }
            
            return list;
        }

        /// <summary>
        /// Get safe string value from data reader
        /// </summary>
        public static string GetSafeString(SqlDataReader reader, string columnName)
        {
            int ordinal = reader.GetOrdinal(columnName);
            return reader.IsDBNull(ordinal) ? string.Empty : reader.GetString(ordinal);
        }

        /// <summary>
        /// Get safe int value from data reader
        /// </summary>
        public static int GetSafeInt(SqlDataReader reader, string columnName)
        {
            int ordinal = reader.GetOrdinal(columnName);
            return reader.IsDBNull(ordinal) ? 0 : reader.GetInt32(ordinal);
        }

        /// <summary>
        /// Get safe DateTime value from data reader
        /// </summary>
        public static DateTime? GetSafeDateTime(SqlDataReader reader, string columnName)
        {
            int ordinal = reader.GetOrdinal(columnName);
            return reader.IsDBNull(ordinal) ? (DateTime?)null : reader.GetDateTime(ordinal);
        }

        /// <summary>
        /// Get safe bool value from data reader
        /// </summary>
        public static bool GetSafeBool(SqlDataReader reader, string columnName)
        {
            int ordinal = reader.GetOrdinal(columnName);
            return reader.IsDBNull(ordinal) ? false : reader.GetBoolean(ordinal);
        }
    }
}
