using System;
using System.Collections.Generic;
using System.Web;
using System.Web.Services;
using System.Web.UI;
using System.Text.RegularExpressions;
using System.Net.Mail;
using System.Configuration;
using ASIWeb.Services;
using ASIWeb.Models;
using ASIWeb.DAL;
using Newtonsoft.Json;

public partial class Pages_ContactHandler : System.Web.UI.Page
{
    protected void Page_Load(object sender, EventArgs e)
    {
        // This page is used only for AJAX calls
    }

    /// <summary>
    /// Submit contact form
    /// </summary>
    [WebMethod]
    public static string SubmitContactForm(string name, string company, string email, string country, string message, string languageCode)
    {
        try
        {
            // Validate input
            List<string> validationErrors = ValidateContactForm(name, company, email, country, message);
            if (validationErrors.Count > 0)
            {
                AjaxResponse errorResponse = new AjaxResponse
                {
                    Success = false,
                    Message = LocalizationService.GetTranslation("form_validation_error", languageCode),
                    Errors = validationErrors
                };
                return JsonConvert.SerializeObject(errorResponse);
            }

            // Create contact submission object
            ContactSubmission submission = new ContactSubmission
            {
                Name = name.Trim(),
                Company = company.Trim(),
                Email = email.Trim().ToLower(),
                Country = country.Trim(),
                Message = message.Trim(),
                LanguageCode = !string.IsNullOrEmpty(languageCode) ? languageCode : "en",
                IPAddress = GetClientIPAddress(),
                UserAgent = GetUserAgent(),
                SubmissionDate = DateTime.Now
            };

            // Save to database
            int submissionId = ContactDAL.InsertContactSubmission(submission);
            
            if (submissionId > 0)
            {
                // Send notification email
                bool emailSent = SendNotificationEmail(submission, submissionId);
                
                AjaxResponse response = new AjaxResponse
                {
                    Success = true,
                    Message = LocalizationService.GetTranslation("form_success", languageCode),
                    Data = new { SubmissionId = submissionId, EmailSent = emailSent }
                };

                return JsonConvert.SerializeObject(response);
            }
            else
            {
                AjaxResponse response = new AjaxResponse
                {
                    Success = false,
                    Message = LocalizationService.GetTranslation("form_error", languageCode),
                    Errors = new List<string> { "Failed to save submission to database" }
                };

                return JsonConvert.SerializeObject(response);
            }
        }
        catch (Exception ex)
        {
            // Log the error
            System.Diagnostics.Debug.WriteLine("Contact form error: " + ex.Message);

            AjaxResponse response = new AjaxResponse
            {
                Success = false,
                Message = LocalizationService.GetTranslation("form_error", languageCode),
                Errors = new List<string> { "An unexpected error occurred. Please try again." }
            };

            return JsonConvert.SerializeObject(response);
        }
    }

    /// <summary>
    /// Validate contact form data
    /// </summary>
    private static List<string> ValidateContactForm(string name, string company, string email, string country, string message)
    {
        List<string> errors = new List<string>();
        string currentLang = LocalizationService.GetCurrentLanguage();

        // Name validation
        if (string.IsNullOrWhiteSpace(name))
        {
            errors.Add(LocalizationService.GetTranslation("field_required", currentLang, "Name is required"));
        }
        else if (name.Trim().Length < 2)
        {
            errors.Add("Name must be at least 2 characters long");
        }

        // Company validation
        if (string.IsNullOrWhiteSpace(company))
        {
            errors.Add(LocalizationService.GetTranslation("field_required", currentLang, "Company is required"));
        }

        // Email validation
        if (string.IsNullOrWhiteSpace(email))
        {
            errors.Add(LocalizationService.GetTranslation("field_required", currentLang, "Email is required"));
        }
        else if (!IsValidEmail(email))
        {
            errors.Add(LocalizationService.GetTranslation("invalid_email", currentLang, "Please enter a valid email address"));
        }

        // Country validation
        if (string.IsNullOrWhiteSpace(country))
        {
            errors.Add(LocalizationService.GetTranslation("field_required", currentLang, "Country is required"));
        }

        // Message validation
        if (string.IsNullOrWhiteSpace(message))
        {
            errors.Add(LocalizationService.GetTranslation("field_required", currentLang, "Message is required"));
        }
        else if (message.Trim().Length < 10)
        {
            errors.Add("Message must be at least 10 characters long");
        }

        return errors;
    }

    /// <summary>
    /// Validate email format
    /// </summary>
    private static bool IsValidEmail(string email)
    {
        if (string.IsNullOrWhiteSpace(email))
            return false;

        try
        {
            // Use regex for basic validation
            string pattern = @"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$";
            return Regex.IsMatch(email, pattern);
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Get client IP address
    /// </summary>
    private static string GetClientIPAddress()
    {
        try
        {
            if (HttpContext.Current != null)
            {
                string ipAddress = HttpContext.Current.Request.ServerVariables["HTTP_X_FORWARDED_FOR"];
                
                if (string.IsNullOrEmpty(ipAddress))
                {
                    ipAddress = HttpContext.Current.Request.ServerVariables["REMOTE_ADDR"];
                }
                
                if (!string.IsNullOrEmpty(ipAddress) && ipAddress.Contains(","))
                {
                    ipAddress = ipAddress.Split(',')[0].Trim();
                }
                
                return ipAddress ?? "Unknown";
            }
        }
        catch
        {
            // Ignore errors
        }
        
        return "Unknown";
    }

    /// <summary>
    /// Get user agent
    /// </summary>
    private static string GetUserAgent()
    {
        try
        {
            if (HttpContext.Current != null && HttpContext.Current.Request != null)
            {
                return HttpContext.Current.Request.UserAgent ?? "Unknown";
            }
        }
        catch
        {
            // Ignore errors
        }
        
        return "Unknown";
    }

    /// <summary>
    /// Send notification email
    /// </summary>
    private static bool SendNotificationEmail(ContactSubmission submission, int submissionId)
    {
        try
        {
            string smtpServer = ContentDAL.GetConfigurationValue("SMTPServer");
            string smtpUsername = ContentDAL.GetConfigurationValue("SMTPUsername");
            string smtpPassword = ContentDAL.GetConfigurationValue("SMTPPassword");
            string contactEmail = ContentDAL.GetConfigurationValue("ContactEmail", "<EMAIL>");
            
            if (string.IsNullOrEmpty(smtpServer) || string.IsNullOrEmpty(smtpUsername))
            {
                return false; // SMTP not configured
            }

            MailMessage mail = new MailMessage();
            mail.From = new MailAddress(smtpUsername, "ASI Website");
            mail.To.Add(contactEmail);
            mail.Subject = string.Format("New Quote Request #{0} from {1}", submissionId, submission.Company);
            
            mail.Body = string.Format(@"
                <h2>New Quote Request</h2>
                <p><strong>Submission ID:</strong> {0}</p>
                <p><strong>Date:</strong> {1}</p>
                <p><strong>Language:</strong> {2}</p>
                <hr>
                <p><strong>Name:</strong> {3}</p>
                <p><strong>Company:</strong> {4}</p>
                <p><strong>Email:</strong> {5}</p>
                <p><strong>Country:</strong> {6}</p>
                <p><strong>Message:</strong></p>
                <p>{7}</p>
                <hr>
                <p><strong>IP Address:</strong> {8}</p>
                <p><strong>User Agent:</strong> {9}</p>
            ", 
            submissionId, 
            submission.SubmissionDate.ToString("yyyy-MM-dd HH:mm:ss"),
            submission.LanguageCode,
            submission.Name,
            submission.Company,
            submission.Email,
            submission.Country,
            submission.Message.Replace("\n", "<br>"),
            submission.IPAddress,
            submission.UserAgent);

            mail.IsBodyHtml = true;

            SmtpClient smtp = new SmtpClient(smtpServer);
            smtp.Port = int.Parse(ContentDAL.GetConfigurationValue("SMTPPort", "587"));
            smtp.Credentials = new System.Net.NetworkCredential(smtpUsername, smtpPassword);
            smtp.EnableSsl = bool.Parse(ContentDAL.GetConfigurationValue("SMTPEnableSSL", "true"));

            smtp.Send(mail);
            return true;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine("Email sending error: " + ex.Message);
            return false;
        }
    }
}
