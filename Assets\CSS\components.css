/* ===================================
   Component-Specific Styles
   Navigation, Hero, Cards, etc.
   =================================== */

/* Language Toggle */
.language-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
}

.language-toggle .btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: 25px;
    font-size: 0.875rem;
    box-shadow: var(--shadow-md);
}

/* Navigation */
.navbar {
    background: var(--white);
    box-shadow: var(--shadow-md);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;
    transition: all var(--transition-normal);
}

.navbar.scrolled {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 80px;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.nav-logo img {
    height: 50px;
    width: auto;
}

.company-name {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--primary-blue);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: var(--spacing-2xl);
    margin: 0;
}

.nav-menu a {
    font-weight: 500;
    font-size: 1rem;
    color: var(--text-dark);
    position: relative;
    transition: color var(--transition-fast);
}

.nav-menu a:hover {
    color: var(--primary-blue);
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--accent-red);
    transition: width var(--transition-normal);
}

.nav-menu a:hover::after {
    width: 100%;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: var(--primary-blue);
    transition: all var(--transition-normal);
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
    color: var(--white);
    padding: 120px 0 80px;
    margin-top: 80px;
    overflow: hidden;
    position: relative;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.1;
}

.hero-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    align-items: center;
    position: relative;
    z-index: 1;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: var(--spacing-lg);
    background: linear-gradient(45deg, var(--white), #e5e7eb);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.25rem;
    line-height: 1.6;
    margin-bottom: var(--spacing-xl);
    color: rgba(255, 255, 255, 0.9);
}

.hero-image img {
    width: 100%;
    height: auto;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
}

/* Section Styles */
.section {
    padding: 80px 0;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: var(--spacing-3xl);
    color: var(--primary-blue);
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: var(--accent-red);
    border-radius: 2px;
}

/* Card Components */
.card {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid #e5e7eb;
}

.card-body {
    padding: var(--spacing-lg);
}

.card-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid #e5e7eb;
    background: var(--light-gray);
}

/* Product Cards */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-xl);
}

.product-card {
    background: var(--white);
    padding: var(--spacing-2xl) var(--spacing-xl);
    border-radius: var(--radius-lg);
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    border: 1px solid rgba(30, 58, 138, 0.1);
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.product-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-lg);
}

.product-icon i {
    font-size: 2rem;
    color: var(--white);
}

.product-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: var(--primary-blue);
}

.product-card p {
    color: var(--text-light);
    line-height: 1.6;
}

/* Benefits Grid */
.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-xl);
}

.benefit-card {
    background: var(--white);
    padding: var(--spacing-2xl) var(--spacing-xl);
    border-radius: var(--radius-lg);
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    border-left: 4px solid var(--accent-red);
}

.benefit-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.benefit-icon {
    width: 70px;
    height: 70px;
    background: var(--accent-red);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-lg);
}

.benefit-icon i {
    font-size: 1.75rem;
    color: var(--white);
}

.benefit-card h3 {
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: var(--primary-blue);
}

/* Client Logos */
.clients-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-2xl);
    align-items: center;
}

.client-logo {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: var(--spacing-lg);
    background: var(--light-gray);
    border-radius: var(--radius-md);
    transition: all var(--transition-normal);
    min-height: 120px;
}

.client-logo:hover {
    background: var(--white);
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.client-logo img {
    max-width: 150px;
    max-height: 80px;
    width: auto;
    height: auto;
    filter: grayscale(100%);
    transition: filter var(--transition-normal);
}

.client-logo:hover img {
    filter: grayscale(0%);
}

/* Testimonials */
.testimonials {
    background: var(--primary-blue);
    color: var(--white);
}

.testimonials .section-title {
    color: var(--white);
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-xl);
}

.testimonial-card {
    background: rgba(255, 255, 255, 0.1);
    padding: var(--spacing-2xl) var(--spacing-xl);
    border-radius: var(--radius-lg);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all var(--transition-normal);
}

.testimonial-card:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-5px);
}

.testimonial-content p {
    font-size: 1.1rem;
    line-height: 1.7;
    margin-bottom: var(--spacing-lg);
    font-style: italic;
}

.testimonial-author strong {
    display: block;
    font-size: 1.1rem;
    margin-bottom: var(--spacing-xs);
}

.testimonial-author span {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.95rem;
}
