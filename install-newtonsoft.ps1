# PowerShell script to download and install Newtonsoft.Json for .NET Framework 4.0
# Run this script to fix the Newtonsoft.Json dependency

Write-Host "Installing Newtonsoft.Json for ASI Web Application..." -ForegroundColor Green

# Create packages directory if it doesn't exist
$packagesDir = "packages"
if (!(Test-Path $packagesDir)) {
    New-Item -ItemType Directory -Path $packagesDir
    Write-Host "Created packages directory" -ForegroundColor Yellow
}

# Create Newtonsoft.Json package directory
$newtonsoftDir = "$packagesDir\Newtonsoft.Json.6.0.8"
if (!(Test-Path $newtonsoftDir)) {
    New-Item -ItemType Directory -Path $newtonsoftDir -Force
    New-Item -ItemType Directory -Path "$newtonsoftDir\lib" -Force
    New-Item -ItemType Directory -Path "$newtonsoftDir\lib\net40" -Force
    Write-Host "Created Newtonsoft.Json package directories" -ForegroundColor Yellow
}

# Download Newtonsoft.Json 6.0.8 DLL for .NET Framework 4.0
$dllUrl = "https://www.nuget.org/api/v2/package/Newtonsoft.Json/6.0.8"
$tempZip = "temp_newtonsoft.zip"
$binDir = "Bin"

try {
    Write-Host "Downloading Newtonsoft.Json 6.0.8..." -ForegroundColor Yellow
    
    # Alternative: Direct DLL download (if available)
    $directDllUrl = "https://globalcdn.nuget.org/packages/newtonsoft.json.6.0.8.nupkg"
    
    # For now, create a note for manual installation
    $installNote = @"
MANUAL INSTALLATION REQUIRED:

1. Download Newtonsoft.Json 6.0.8 from NuGet:
   https://www.nuget.org/packages/Newtonsoft.Json/6.0.8

2. Extract the .nupkg file (rename to .zip and extract)

3. Copy the DLL from: lib\net40\Newtonsoft.Json.dll
   To: $binDir\Newtonsoft.Json.dll

4. Or use Visual Studio Package Manager Console:
   Install-Package Newtonsoft.Json -Version 6.0.8

ALTERNATIVE - Use NuGet CLI:
1. Download nuget.exe from https://www.nuget.org/downloads
2. Run: nuget.exe install Newtonsoft.Json -Version 6.0.8 -OutputDirectory packages
3. Copy from packages\Newtonsoft.Json.6.0.8\lib\net40\Newtonsoft.Json.dll to Bin\

"@

    Write-Host $installNote -ForegroundColor Cyan
    
    # Create the install note file
    $installNote | Out-File -FilePath "NEWTONSOFT_INSTALL_INSTRUCTIONS.txt" -Encoding UTF8
    Write-Host "Installation instructions saved to NEWTONSOFT_INSTALL_INSTRUCTIONS.txt" -ForegroundColor Green
    
} catch {
    Write-Host "Error during installation: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please install manually using the instructions above." -ForegroundColor Yellow
}

Write-Host "Script completed. Please follow the manual installation steps if automatic download failed." -ForegroundColor Green
